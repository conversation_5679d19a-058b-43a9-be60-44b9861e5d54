import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:camera/camera.dart';
import 'package:geolocator/geolocator.dart';
import '../services/camera_service.dart';
import '../models/image_model.dart';
import '../models/user_settings.dart';
import 'app_providers.dart';

// Camera service provider
final cameraServiceProvider = Provider<CameraService>((ref) {
  return CameraService.instance;
});

// Camera state provider
final cameraProvider = StateNotifierProvider<CameraNotifier, CameraState>((ref) {
  return CameraNotifier(
    ref.read(cameraServiceProvider),
    ref.read(imagesProvider.notifier),
    ref.read(userSettingsProvider),
  );
});

class CameraState {
  final bool isInitialized;
  final bool isCapturing;
  final bool isRecording;
  final FlashMode flashMode;
  final double zoomLevel;
  final double exposureOffset;
  final String? error;

  const CameraState({
    this.isInitialized = false,
    this.isCapturing = false,
    this.isRecording = false,
    this.flashMode = FlashMode.auto,
    this.zoomLevel = 1.0,
    this.exposureOffset = 0.0,
    this.error,
  });

  CameraState copyWith({
    bool? isInitialized,
    bool? isCapturing,
    bool? isRecording,
    FlashMode? flashMode,
    double? zoomLevel,
    double? exposureOffset,
    String? error,
  }) {
    return CameraState(
      isInitialized: isInitialized ?? this.isInitialized,
      isCapturing: isCapturing ?? this.isCapturing,
      isRecording: isRecording ?? this.isRecording,
      flashMode: flashMode ?? this.flashMode,
      zoomLevel: zoomLevel ?? this.zoomLevel,
      exposureOffset: exposureOffset ?? this.exposureOffset,
      error: error,
    );
  }
}

class CameraNotifier extends StateNotifier<CameraState> {
  final CameraService _cameraService;
  final ImagesNotifier _imagesNotifier;
  final UserSettings _userSettings;

  CameraNotifier(this._cameraService, this._imagesNotifier, this._userSettings) 
      : super(const CameraState());

  Future<void> initialize() async {
    try {
      state = state.copyWith(error: null);
      final bool success = await _cameraService.initialize();
      
      if (success) {
        state = state.copyWith(isInitialized: true);
      } else {
        state = state.copyWith(
          isInitialized: false,
          error: 'Failed to initialize camera',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isInitialized: false,
        error: 'Camera initialization error: $e',
      );
    }
  }

  Future<ImageModel?> captureImage({
    CaptureType captureType = CaptureType.manual,
  }) async {
    if (!state.isInitialized) {
      state = state.copyWith(error: 'Camera not initialized');
      return null;
    }

    try {
      state = state.copyWith(isCapturing: true, error: null);

      // Get location if enabled
      double? latitude;
      double? longitude;
      
      if (_userSettings.locationTrackingEnabled) {
        try {
          final Position position = await _getCurrentPosition();
          latitude = position.latitude;
          longitude = position.longitude;
        } catch (e) {
          print('Location error: $e');
          // Continue without location
        }
      }

      final ImageModel? imageModel = await _cameraService.captureImage(
        captureType: captureType,
        latitude: latitude,
        longitude: longitude,
      );

      if (imageModel != null) {
        await _imagesNotifier.addImage(imageModel);
        state = state.copyWith(isCapturing: false);
        return imageModel;
      } else {
        state = state.copyWith(
          isCapturing: false,
          error: 'Failed to capture image',
        );
        return null;
      }
    } catch (e) {
      state = state.copyWith(
        isCapturing: false,
        error: 'Capture error: $e',
      );
      return null;
    }
  }

  Future<Position> _getCurrentPosition() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw Exception('Location services are disabled');
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw Exception('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception('Location permissions are permanently denied');
    }

    return await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );
  }

  Future<void> switchCamera() async {
    if (!state.isInitialized) return;
    
    try {
      await _cameraService.switchCamera();
      state = state.copyWith(error: null);
    } catch (e) {
      state = state.copyWith(error: 'Failed to switch camera: $e');
    }
  }

  Future<void> setFlashMode(FlashMode flashMode) async {
    if (!state.isInitialized) return;
    
    try {
      await _cameraService.setFlashMode(flashMode);
      state = state.copyWith(flashMode: flashMode, error: null);
    } catch (e) {
      state = state.copyWith(error: 'Failed to set flash mode: $e');
    }
  }

  Future<void> setZoomLevel(double zoom) async {
    if (!state.isInitialized) return;
    
    try {
      await _cameraService.setZoomLevel(zoom);
      state = state.copyWith(zoomLevel: zoom, error: null);
    } catch (e) {
      state = state.copyWith(error: 'Failed to set zoom: $e');
    }
  }

  Future<void> setExposureOffset(double offset) async {
    if (!state.isInitialized) return;
    
    try {
      await _cameraService.setExposureOffset(offset);
      state = state.copyWith(exposureOffset: offset, error: null);
    } catch (e) {
      state = state.copyWith(error: 'Failed to set exposure: $e');
    }
  }

  Future<void> focusOnPoint(Offset point) async {
    if (!state.isInitialized) return;
    
    try {
      await _cameraService.focusOnPoint(point);
      state = state.copyWith(error: null);
    } catch (e) {
      state = state.copyWith(error: 'Failed to focus: $e');
    }
  }

  Future<void> startVideoRecording() async {
    if (!state.isInitialized || state.isRecording) return;
    
    try {
      await _cameraService.startVideoRecording();
      state = state.copyWith(isRecording: true, error: null);
    } catch (e) {
      state = state.copyWith(error: 'Failed to start recording: $e');
    }
  }

  Future<void> stopVideoRecording() async {
    if (!state.isInitialized || !state.isRecording) return;
    
    try {
      await _cameraService.stopVideoRecording();
      state = state.copyWith(isRecording: false, error: null);
    } catch (e) {
      state = state.copyWith(error: 'Failed to stop recording: $e');
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  @override
  Future<void> dispose() async {
    await _cameraService.dispose();
    super.dispose();
  }

  // Getters for camera capabilities
  bool get hasMultipleCameras => _cameraService.hasMultipleCameras;
  bool get isRecording => _cameraService.isRecording;
  bool get isPreviewPaused => _cameraService.isPreviewPaused;
  FlashMode get currentFlashMode => _cameraService.currentFlashMode;
}
