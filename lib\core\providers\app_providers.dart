import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/image_model.dart';
import '../models/user_settings.dart';
import '../services/storage_service.dart';
import '../services/api_service.dart';

// Storage service provider
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService.instance;
});

// API service provider
final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService.instance;
});

// User settings provider
final userSettingsProvider = StateNotifierProvider<UserSettingsNotifier, UserSettings>((ref) {
  return UserSettingsNotifier(ref.read(storageServiceProvider));
});

class UserSettingsNotifier extends StateNotifier<UserSettings> {
  final StorageService _storageService;

  UserSettingsNotifier(this._storageService) : super(const UserSettings()) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final settings = await _storageService.getSettings();
    state = settings;
  }

  Future<void> updateSettings(UserSettings newSettings) async {
    await _storageService.saveSettings(newSettings);
    state = newSettings;
  }

  Future<void> toggleTracking() async {
    final newSettings = state.copyWith(isTrackingEnabled: !state.isTrackingEnabled);
    await updateSettings(newSettings);
  }

  Future<void> updateCaptureInterval(int interval) async {
    final newSettings = state.copyWith(captureInterval: interval);
    await updateSettings(newSettings);
  }

  Future<void> toggleMotionDetection() async {
    final newSettings = state.copyWith(motionDetectionEnabled: !state.motionDetectionEnabled);
    await updateSettings(newSettings);
  }

  Future<void> toggleLocationTracking() async {
    final newSettings = state.copyWith(locationTrackingEnabled: !state.locationTrackingEnabled);
    await updateSettings(newSettings);
  }

  Future<void> addPrivacyZone(PrivacyZone zone) async {
    final newZones = [...state.privacyZones, zone];
    final newSettings = state.copyWith(privacyZones: newZones);
    await updateSettings(newSettings);
  }

  Future<void> removePrivacyZone(String zoneId) async {
    final newZones = state.privacyZones.where((zone) => zone.id != zoneId).toList();
    final newSettings = state.copyWith(privacyZones: newZones);
    await updateSettings(newSettings);
  }

  Future<void> toggleDarkMode() async {
    final newSettings = state.copyWith(darkModeEnabled: !state.darkModeEnabled);
    await updateSettings(newSettings);
  }

  Future<void> updateImageQuality(int quality) async {
    final newSettings = state.copyWith(imageQuality: quality);
    await updateSettings(newSettings);
  }

  Future<void> updatePreferredAiModel(String model) async {
    final newSettings = state.copyWith(preferredAiModel: model);
    await updateSettings(newSettings);
  }
}

// App state provider
final appStateProvider = StateNotifierProvider<AppStateNotifier, AppState>((ref) {
  return AppStateNotifier(ref.read(storageServiceProvider));
});

class AppStateNotifier extends StateNotifier<AppState> {
  final StorageService _storageService;

  AppStateNotifier(this._storageService) : super(const AppState()) {
    _loadAppState();
  }

  Future<void> _loadAppState() async {
    final appState = await _storageService.getAppState();
    state = appState;
  }

  Future<void> updateAppState(AppState newState) async {
    await _storageService.saveAppState(newState);
    state = newState;
  }

  Future<void> completeOnboarding() async {
    final newState = state.copyWith(
      hasCompletedOnboarding: true,
      isFirstLaunch: false,
    );
    await updateAppState(newState);
  }

  Future<void> updateBackgroundServiceStatus(bool isRunning) async {
    final newState = state.copyWith(isBackgroundServiceRunning: isRunning);
    await updateAppState(newState);
  }

  Future<void> updateImageCounts(int total, int unanalyzed) async {
    final newState = state.copyWith(
      totalImagesCount: total,
      unanalyzedImagesCount: unanalyzed,
    );
    await updateAppState(newState);
  }

  Future<void> updateLastSyncTime() async {
    final newState = state.copyWith(lastSyncTime: DateTime.now());
    await updateAppState(newState);
  }

  Future<void> setError(String error) async {
    final newState = state.copyWith(
      lastError: error,
      lastErrorTime: DateTime.now(),
    );
    await updateAppState(newState);
  }

  Future<void> clearError() async {
    final newState = state.copyWith(
      lastError: null,
      lastErrorTime: null,
    );
    await updateAppState(newState);
  }
}

// Images provider
final imagesProvider = StateNotifierProvider<ImagesNotifier, List<ImageModel>>((ref) {
  return ImagesNotifier(ref.read(storageServiceProvider));
});

class ImagesNotifier extends StateNotifier<List<ImageModel>> {
  final StorageService _storageService;

  ImagesNotifier(this._storageService) : super([]) {
    _loadImages();
  }

  Future<void> _loadImages() async {
    final images = await _storageService.getAllImages();
    images.sort((a, b) => b.capturedAt.compareTo(a.capturedAt)); // Sort by newest first
    state = images;
  }

  Future<void> addImage(ImageModel image) async {
    await _storageService.saveImage(image);
    state = [image, ...state];
  }

  Future<void> updateImage(ImageModel updatedImage) async {
    await _storageService.saveImage(updatedImage);
    state = state.map((image) => 
      image.id == updatedImage.id ? updatedImage : image
    ).toList();
  }

  Future<void> deleteImage(String imageId) async {
    await _storageService.deleteImage(imageId);
    state = state.where((image) => image.id != imageId).toList();
  }

  Future<void> deleteMultipleImages(List<String> imageIds) async {
    for (final id in imageIds) {
      await _storageService.deleteImage(id);
    }
    state = state.where((image) => !imageIds.contains(image.id)).toList();
  }

  Future<void> refreshImages() async {
    await _loadImages();
  }

  List<ImageModel> getImagesByDate(DateTime date) {
    return state.where((image) {
      final imageDate = DateTime(
        image.capturedAt.year,
        image.capturedAt.month,
        image.capturedAt.day,
      );
      final targetDate = DateTime(date.year, date.month, date.day);
      return imageDate.isAtSameMomentAs(targetDate);
    }).toList();
  }

  List<ImageModel> getImagesByDateRange(DateTime start, DateTime end) {
    return state.where((image) =>
      image.capturedAt.isAfter(start) && image.capturedAt.isBefore(end)
    ).toList();
  }
}

// Analytics provider
final analyticsProvider = StateNotifierProvider<AnalyticsNotifier, List<AnalyticsData>>((ref) {
  return AnalyticsNotifier(ref.read(storageServiceProvider));
});

class AnalyticsNotifier extends StateNotifier<List<AnalyticsData>> {
  final StorageService _storageService;

  AnalyticsNotifier(this._storageService) : super([]) {
    _loadAnalytics();
  }

  Future<void> _loadAnalytics() async {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(const Duration(days: 30));
    final analytics = await _storageService.getAnalyticsRange(startDate, endDate);
    state = analytics;
  }

  Future<void> addAnalytics(AnalyticsData analytics) async {
    await _storageService.saveAnalytics(analytics);
    state = [...state, analytics];
  }

  Future<void> refreshAnalytics() async {
    await _loadAnalytics();
  }
}
