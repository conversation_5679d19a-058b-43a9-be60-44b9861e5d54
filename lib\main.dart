import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/services/storage_service.dart';
import 'core/services/api_service.dart';
import 'core/services/background_service.dart';
import 'presentation/app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  await StorageService.instance.initialize();
  ApiService.instance.initialize();
  await BackgroundService.instance.initialize();

  runApp(
    const ProviderScope(
      child: LifeTrackerApp(),
    ),
  );
}
