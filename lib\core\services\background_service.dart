import 'dart:async';
import 'dart:io';
import 'package:workmanager/workmanager.dart';
import 'package:geolocator/geolocator.dart';
import '../constants/app_constants.dart';
import '../models/image_model.dart';
import '../models/user_settings.dart';
import 'storage_service.dart';
import 'camera_service.dart';
import 'api_service.dart';

class BackgroundService {
  static BackgroundService? _instance;
  static BackgroundService get instance => _instance ??= BackgroundService._();
  BackgroundService._();

  bool _isRunning = false;
  Timer? _captureTimer;
  StreamSubscription<Position>? _locationSubscription;

  bool get isRunning => _isRunning;

  Future<void> initialize() async {
    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: false, // Set to true for debugging
    );
  }

  Future<void> startBackgroundCapture() async {
    if (_isRunning) return;

    try {
      // Register periodic task
      await Workmanager().registerPeriodicTask(
        AppConstants.backgroundTaskName,
        AppConstants.backgroundTaskName,
        frequency: Duration(minutes: AppConstants.backgroundTaskFrequency),
        constraints: Constraints(
          networkType: NetworkType.not_required,
          requiresBatteryNotLow: true,
          requiresCharging: false,
          requiresDeviceIdle: false,
          requiresStorageNotLow: true,
        ),
      );

      _isRunning = true;
      print('Background capture service started');
    } catch (e) {
      print('Failed to start background service: $e');
    }
  }

  Future<void> stopBackgroundCapture() async {
    if (!_isRunning) return;

    try {
      await Workmanager().cancelByUniqueName(AppConstants.backgroundTaskName);
      _captureTimer?.cancel();
      _locationSubscription?.cancel();
      
      _isRunning = false;
      print('Background capture service stopped');
    } catch (e) {
      print('Failed to stop background service: $e');
    }
  }

  Future<void> startForegroundCapture(UserSettings settings) async {
    if (_captureTimer != null) return;

    _captureTimer = Timer.periodic(
      Duration(seconds: settings.captureInterval),
      (timer) async {
        await _performCapture(settings);
      },
    );

    print('Foreground capture started with ${settings.captureInterval}s interval');
  }

  Future<void> stopForegroundCapture() async {
    _captureTimer?.cancel();
    _captureTimer = null;
    print('Foreground capture stopped');
  }

  Future<void> _performCapture(UserSettings settings) async {
    try {
      // Check if we're in a privacy zone
      if (settings.locationTrackingEnabled && settings.privacyZones.isNotEmpty) {
        final Position? position = await _getCurrentPosition();
        if (position != null && _isInPrivacyZone(position, settings.privacyZones)) {
          print('Skipping capture - in privacy zone');
          return;
        }
      }

      // Check active hours
      if (!_isInActiveHours(settings.activeHours)) {
        print('Skipping capture - outside active hours');
        return;
      }

      // Initialize camera service
      final cameraService = CameraService.instance;
      if (!cameraService.isInitialized) {
        final success = await cameraService.initialize();
        if (!success) {
          print('Failed to initialize camera for background capture');
          return;
        }
      }

      // Capture image
      final ImageModel? image = await cameraService.captureImage(
        captureType: CaptureType.automatic,
        latitude: settings.locationTrackingEnabled ? (await _getCurrentPosition())?.latitude : null,
        longitude: settings.locationTrackingEnabled ? (await _getCurrentPosition())?.longitude : null,
      );

      if (image != null) {
        // Save to local storage
        await StorageService.instance.saveImage(image);
        print('Background image captured: ${image.id}');

        // Upload to backend if available
        _uploadImageInBackground(image);
      }
    } catch (e) {
      print('Background capture error: $e');
    }
  }

  Future<Position?> _getCurrentPosition() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) return null;

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) return null;
      }

      if (permission == LocationPermission.deniedForever) return null;

      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.medium,
        timeLimit: const Duration(seconds: 10),
      );
    } catch (e) {
      print('Location error: $e');
      return null;
    }
  }

  bool _isInPrivacyZone(Position position, List<PrivacyZone> privacyZones) {
    for (final zone in privacyZones) {
      if (!zone.isActive) continue;
      
      final double distance = Geolocator.distanceBetween(
        position.latitude,
        position.longitude,
        zone.latitude,
        zone.longitude,
      );
      
      if (distance <= zone.radius) {
        return true;
      }
    }
    return false;
  }

  bool _isInActiveHours(List<String> activeHours) {
    if (activeHours.length != 2) return true; // If not properly configured, allow all hours
    
    try {
      final now = DateTime.now();
      final currentTime = now.hour * 60 + now.minute; // Convert to minutes
      
      final startParts = activeHours[0].split(':');
      final endParts = activeHours[1].split(':');
      
      final startTime = int.parse(startParts[0]) * 60 + int.parse(startParts[1]);
      final endTime = int.parse(endParts[0]) * 60 + int.parse(endParts[1]);
      
      if (startTime <= endTime) {
        // Same day range (e.g., 09:00 to 17:00)
        return currentTime >= startTime && currentTime <= endTime;
      } else {
        // Overnight range (e.g., 22:00 to 06:00)
        return currentTime >= startTime || currentTime <= endTime;
      }
    } catch (e) {
      print('Error parsing active hours: $e');
      return true; // Default to allowing capture
    }
  }

  void _uploadImageInBackground(ImageModel image) {
    // Fire and forget upload
    Future.microtask(() async {
      try {
        final apiService = ApiService.instance;
        final result = await apiService.uploadImage(
          File(image.localPath),
          image,
        );
        
        if (result != null) {
          // Update image with remote path
          final updatedImage = image.copyWith(
            remotePath: result['remote_path'],
            isUploaded: true,
          );
          await StorageService.instance.saveImage(updatedImage);
          print('Background upload successful: ${image.id}');
        }
      } catch (e) {
        print('Background upload error: $e');
      }
    });
  }

  Future<void> scheduleMotionDetection() async {
    // TODO: Implement motion detection using device sensors
    // This would use accelerometer/gyroscope to detect movement
    // and trigger image capture when motion is detected
  }

  Future<void> cleanupOldImages(UserSettings settings) async {
    if (!settings.autoDeleteOldImages) return;
    
    try {
      await StorageService.instance.deleteOldImages(settings.dataRetentionDays);
      print('Old images cleaned up');
    } catch (e) {
      print('Cleanup error: $e');
    }
  }
}

// Background task callback
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      print('Background task executed: $task');
      
      // Initialize services
      await StorageService.instance.initialize();
      ApiService.instance.initialize();
      
      // Get user settings
      final settings = await StorageService.instance.getSettings();
      
      if (!settings.isTrackingEnabled) {
        print('Tracking disabled, skipping background task');
        return Future.value(true);
      }
      
      // Perform background capture
      final backgroundService = BackgroundService.instance;
      await backgroundService._performCapture(settings);
      
      // Cleanup old images if needed
      await backgroundService.cleanupOldImages(settings);
      
      return Future.value(true);
    } catch (e) {
      print('Background task error: $e');
      return Future.value(false);
    }
  });
}

// Motion detection service (placeholder)
class MotionDetectionService {
  static MotionDetectionService? _instance;
  static MotionDetectionService get instance => _instance ??= MotionDetectionService._();
  MotionDetectionService._();

  bool _isListening = false;
  StreamSubscription? _accelerometerSubscription;
  DateTime? _lastMotionTime;
  
  bool get isListening => _isListening;

  Future<void> startMotionDetection() async {
    if (_isListening) return;
    
    try {
      // TODO: Implement accelerometer/gyroscope listening
      // This would use sensors_plus package to detect device motion
      // and trigger image capture when significant movement is detected
      
      _isListening = true;
      print('Motion detection started');
    } catch (e) {
      print('Motion detection start error: $e');
    }
  }

  Future<void> stopMotionDetection() async {
    _accelerometerSubscription?.cancel();
    _isListening = false;
    print('Motion detection stopped');
  }

  void _onMotionDetected() {
    final now = DateTime.now();
    
    // Debounce motion events (don't trigger too frequently)
    if (_lastMotionTime != null && 
        now.difference(_lastMotionTime!).inSeconds < 30) {
      return;
    }
    
    _lastMotionTime = now;
    
    // Trigger image capture
    Future.microtask(() async {
      try {
        final cameraService = CameraService.instance;
        if (!cameraService.isInitialized) {
          await cameraService.initialize();
        }
        
        final image = await cameraService.captureImage(
          captureType: CaptureType.motionTriggered,
        );
        
        if (image != null) {
          await StorageService.instance.saveImage(image);
          print('Motion-triggered capture: ${image.id}');
        }
      } catch (e) {
        print('Motion capture error: $e');
      }
    });
  }
}
