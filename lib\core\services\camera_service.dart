import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'package:image/image.dart' as img;
import '../models/image_model.dart';
import '../constants/app_constants.dart';

class CameraService {
  static CameraService? _instance;
  static CameraService get instance => _instance ??= CameraService._();
  CameraService._();

  CameraController? _controller;
  List<CameraDescription> _cameras = [];
  bool _isInitialized = false;
  final Uuid _uuid = const Uuid();

  bool get isInitialized => _isInitialized;
  CameraController? get controller => _controller;
  List<CameraDescription> get cameras => _cameras;

  Future<bool> initialize() async {
    try {
      _cameras = await availableCameras();
      if (_cameras.isEmpty) {
        return false;
      }

      // Use the first available camera (usually back camera)
      _controller = CameraController(
        _cameras.first,
        ResolutionPreset.high,
        enableAudio: false,
      );

      await _controller!.initialize();
      _isInitialized = true;
      return true;
    } catch (e) {
      print('Camera initialization error: $e');
      return false;
    }
  }

  Future<ImageModel?> captureImage({
    CaptureType captureType = CaptureType.manual,
    double? latitude,
    double? longitude,
  }) async {
    if (!_isInitialized || _controller == null) {
      throw Exception('Camera not initialized');
    }

    try {
      final XFile imageFile = await _controller!.takePicture();
      
      // Create unique filename
      final String imageId = _uuid.v4();
      final String fileName = '$imageId.jpg';
      
      // Get app directory
      final Directory appDir = await getApplicationDocumentsDirectory();
      final Directory imagesDir = Directory(path.join(appDir.path, AppConstants.imagesDirectory));
      
      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
      }
      
      final String finalPath = path.join(imagesDir.path, fileName);
      
      // Process and compress image
      final File processedFile = await _processImage(File(imageFile.path), finalPath);
      
      // Get file size
      final int fileSize = await processedFile.length();
      
      // Create ImageModel
      final ImageModel imageModel = ImageModel(
        id: imageId,
        localPath: finalPath,
        capturedAt: DateTime.now(),
        latitude: latitude,
        longitude: longitude,
        captureType: captureType,
        fileSize: fileSize,
      );
      
      // Clean up temporary file
      await File(imageFile.path).delete();
      
      return imageModel;
    } catch (e) {
      print('Image capture error: $e');
      return null;
    }
  }

  Future<File> _processImage(File originalFile, String outputPath) async {
    try {
      // Read original image
      final Uint8List imageBytes = await originalFile.readAsBytes();
      img.Image? image = img.decodeImage(imageBytes);
      
      if (image == null) {
        throw Exception('Failed to decode image');
      }
      
      // Resize if too large
      if (image.width > AppConstants.maxImageSize || image.height > AppConstants.maxImageSize) {
        image = img.copyResize(
          image,
          width: image.width > image.height ? AppConstants.maxImageSize : null,
          height: image.height > image.width ? AppConstants.maxImageSize : null,
        );
      }
      
      // Compress and save
      final List<int> compressedBytes = img.encodeJpg(image, quality: AppConstants.imageQuality);
      final File outputFile = File(outputPath);
      await outputFile.writeAsBytes(compressedBytes);
      
      return outputFile;
    } catch (e) {
      print('Image processing error: $e');
      // If processing fails, just copy the original
      return await originalFile.copy(outputPath);
    }
  }

  Future<void> switchCamera() async {
    if (!_isInitialized || _cameras.length < 2) return;
    
    final currentCamera = _controller!.description;
    final newCamera = _cameras.firstWhere(
      (camera) => camera != currentCamera,
      orElse: () => _cameras.first,
    );
    
    await _controller!.dispose();
    _controller = CameraController(
      newCamera,
      ResolutionPreset.high,
      enableAudio: false,
    );
    
    await _controller!.initialize();
  }

  Future<void> setFlashMode(FlashMode flashMode) async {
    if (!_isInitialized || _controller == null) return;
    await _controller!.setFlashMode(flashMode);
  }

  Future<void> setZoomLevel(double zoom) async {
    if (!_isInitialized || _controller == null) return;
    
    final double maxZoom = await _controller!.getMaxZoomLevel();
    final double minZoom = await _controller!.getMinZoomLevel();
    
    final double clampedZoom = zoom.clamp(minZoom, maxZoom);
    await _controller!.setZoomLevel(clampedZoom);
  }

  Future<void> setExposureOffset(double offset) async {
    if (!_isInitialized || _controller == null) return;
    
    final double maxExposure = await _controller!.getMaxExposureOffset();
    final double minExposure = await _controller!.getMinExposureOffset();
    
    final double clampedOffset = offset.clamp(minExposure, maxExposure);
    await _controller!.setExposureOffset(clampedOffset);
  }

  Future<void> focusOnPoint(Offset point) async {
    if (!_isInitialized || _controller == null) return;
    await _controller!.setFocusPoint(point);
    await _controller!.setExposurePoint(point);
  }

  Future<void> startVideoRecording() async {
    if (!_isInitialized || _controller == null) return;
    if (_controller!.value.isRecordingVideo) return;
    
    await _controller!.startVideoRecording();
  }

  Future<XFile?> stopVideoRecording() async {
    if (!_isInitialized || _controller == null) return null;
    if (!_controller!.value.isRecordingVideo) return null;
    
    return await _controller!.stopVideoRecording();
  }

  Future<void> pausePreview() async {
    if (!_isInitialized || _controller == null) return;
    await _controller!.pausePreview();
  }

  Future<void> resumePreview() async {
    if (!_isInitialized || _controller == null) return;
    await _controller!.resumePreview();
  }

  Future<void> dispose() async {
    if (_controller != null) {
      await _controller!.dispose();
      _controller = null;
    }
    _isInitialized = false;
  }

  // Utility methods
  bool get hasMultipleCameras => _cameras.length > 1;
  
  bool get isRecording => _controller?.value.isRecordingVideo ?? false;
  
  bool get isPreviewPaused => _controller?.value.isPreviewPaused ?? false;
  
  FlashMode get currentFlashMode => _controller?.value.flashMode ?? FlashMode.auto;
  
  Future<double> get currentZoomLevel async {
    if (!_isInitialized || _controller == null) return 1.0;
    return 1.0; // Camera package doesn't have getZoomLevel in current version
  }
  
  Future<double> get maxZoomLevel async {
    if (!_isInitialized || _controller == null) return 1.0;
    return await _controller!.getMaxZoomLevel();
  }
  
  Future<double> get minZoomLevel async {
    if (!_isInitialized || _controller == null) return 1.0;
    return await _controller!.getMinZoomLevel();
  }
}
