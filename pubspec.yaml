name: life_tracker
description: "A comprehensive Flutter mobile app for personal activity monitoring and AI-powered insights."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI and Icons
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296

  # State Management
  flutter_riverpod: ^2.4.10

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Camera & Image Processing
  camera: ^0.10.5+9
  image: ^4.1.7
  path_provider: ^2.1.2

  # Background Services
  workmanager: ^0.5.2

  # HTTP & WebSocket
  http: ^1.2.0
  web_socket_channel: ^2.4.0
  dio: ^5.4.0

  # Charts & Analytics
  fl_chart: ^0.66.2

  # Permissions
  permission_handler: ^11.2.0

  # Location Services
  geolocator: ^10.1.0

  # Date & Time
  intl: ^0.19.0

  # Path utilities
  path: ^1.8.3

  # JSON Serialization
  json_annotation: ^4.8.1

  # Utilities
  uuid: ^4.3.3
  crypto: ^3.0.3
  shared_preferences: ^2.2.2

  # File handling
  file_picker: ^6.1.1
  share_plus: ^7.2.2

  # Speech to text
  speech_to_text: ^6.6.0

  # Animations
  lottie: ^3.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code generation
  build_runner: ^2.4.7
  hive_generator: ^2.0.1
  json_serializable: ^6.7.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
