import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:intl/intl.dart';
import '../../core/constants/app_constants.dart';
import '../../core/providers/app_providers.dart';
import '../../core/providers/background_service_provider.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/recent_activity_card.dart';
import '../widgets/quick_stats_card.dart';
import 'camera_screen.dart';

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appState = ref.watch(appStateProvider);
    final userSettings = ref.watch(userSettingsProvider);
    final images = ref.watch(imagesProvider);
    final backgroundServiceState = ref.watch(backgroundServiceStateProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(AppConstants.appName),
        actions: [
          IconButton(
            icon: Icon(
              userSettings.isTrackingEnabled 
                  ? MdiIcons.recordCircle 
                  : MdiIcons.recordCircleOutline,
              color: userSettings.isTrackingEnabled 
                  ? Colors.red 
                  : null,
            ),
            onPressed: () {
              ref.read(userSettingsProvider.notifier).toggleTracking();
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await ref.read(imagesProvider.notifier).refreshImages();
          await ref.read(analyticsProvider.notifier).refreshAnalytics();
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSizes.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Section
              _buildWelcomeSection(context),
              const SizedBox(height: AppSizes.paddingL),

              // Quick Stats
              QuickStatsCard(
                totalImages: appState.totalImagesCount,
                unanalyzedImages: appState.unanalyzedImagesCount,
                isTracking: userSettings.isTrackingEnabled,
                lastSync: appState.lastSyncTime,
              ),
              const SizedBox(height: AppSizes.paddingL),

              // Status Cards
              Row(
                children: [
                  Expanded(
                    child: DashboardCard(
                      title: 'Tracking Status',
                      subtitle: userSettings.isTrackingEnabled 
                          ? 'Active' 
                          : 'Paused',
                      icon: userSettings.isTrackingEnabled 
                          ? MdiIcons.play 
                          : MdiIcons.pause,
                      color: userSettings.isTrackingEnabled 
                          ? Colors.green 
                          : Colors.orange,
                      onTap: () {
                        ref.read(userSettingsProvider.notifier).toggleTracking();
                      },
                    ),
                  ),
                  const SizedBox(width: AppSizes.paddingM),
                  Expanded(
                    child: DashboardCard(
                      title: 'Background Service',
                      subtitle: backgroundServiceState.isRunning
                          ? 'Running'
                          : 'Stopped',
                      icon: backgroundServiceState.isRunning
                          ? MdiIcons.cloudCheck
                          : MdiIcons.cloudOff,
                      color: backgroundServiceState.isRunning
                          ? Colors.blue
                          : Colors.grey,
                      onTap: () {
                        ref.read(backgroundServiceStateProvider.notifier).toggleBackgroundService();
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppSizes.paddingL),

              // Recent Activity
              RecentActivityCard(
                images: images.take(5).toList(),
              ),
              const SizedBox(height: AppSizes.paddingL),

              // Quick Actions
              _buildQuickActions(context, ref),
              const SizedBox(height: AppSizes.paddingL),

              // Today's Insights
              _buildTodayInsights(context, images),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    final now = DateTime.now();
    final hour = now.hour;
    String greeting;
    
    if (hour < 12) {
      greeting = 'Good Morning';
    } else if (hour < 17) {
      greeting = 'Good Afternoon';
    } else {
      greeting = 'Good Evening';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          greeting,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          DateFormat('EEEE, MMMM d').format(now),
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingM),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _QuickActionButton(
                  icon: MdiIcons.camera,
                  label: 'Capture',
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CameraScreen(),
                      ),
                    );
                  },
                ),
                _QuickActionButton(
                  icon: MdiIcons.chat,
                  label: 'Ask AI',
                  onTap: () {
                    ref.read(currentIndexProvider.notifier).state = 3;
                  },
                ),
                _QuickActionButton(
                  icon: MdiIcons.chartLine,
                  label: 'Analytics',
                  onTap: () {
                    ref.read(currentIndexProvider.notifier).state = 2;
                  },
                ),
                _QuickActionButton(
                  icon: MdiIcons.cog,
                  label: 'Settings',
                  onTap: () {
                    ref.read(currentIndexProvider.notifier).state = 5;
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTodayInsights(BuildContext context, List<dynamic> images) {
    final today = DateTime.now();
    final todayImages = images.where((image) {
      final imageDate = DateTime(
        image.capturedAt.year,
        image.capturedAt.month,
        image.capturedAt.day,
      );
      final targetDate = DateTime(today.year, today.month, today.day);
      return imageDate.isAtSameMomentAs(targetDate);
    }).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Today\'s Insights',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingM),
            Row(
              children: [
                Icon(
                  MdiIcons.imageMultiple,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: AppSizes.paddingS),
                Text('${todayImages.length} images captured today'),
              ],
            ),
            const SizedBox(height: AppSizes.paddingS),
            Row(
              children: [
                Icon(
                  MdiIcons.clock,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: AppSizes.paddingS),
                Text('Active since ${DateFormat('HH:mm').format(DateTime.now().subtract(const Duration(hours: 8)))}'),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _QuickActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;

  const _QuickActionButton({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppSizes.radiusM),
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingS),
        child: Column(
          children: [
            Icon(
              icon,
              size: AppSizes.iconL,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: AppSizes.paddingXS),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }
}

// Provider for current index (needed for navigation)
final currentIndexProvider = StateProvider<int>((ref) => 0);
