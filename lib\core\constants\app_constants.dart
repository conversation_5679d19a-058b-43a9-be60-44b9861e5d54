class AppConstants {
  // App Info
  static const String appName = 'Life Tracker';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String baseUrl = 'http://localhost:5000'; // Update with your backend URL
  static const String apiVersion = '/api/v1';
  static const String wsUrl = 'ws://localhost:5000/ws'; // WebSocket URL
  
  // Storage Keys
  static const String userPrefsBox = 'user_preferences';
  static const String imagesBox = 'images';
  static const String analyticsBox = 'analytics';
  static const String chatBox = 'chat_history';
  
  // Image Capture Settings
  static const int defaultCaptureInterval = 300; // 5 minutes in seconds
  static const int maxImageSize = 1920; // Max width/height in pixels
  static const int imageQuality = 85; // JPEG quality (0-100)
  static const int maxStoredImages = 1000; // Maximum images to store locally
  
  // Privacy Settings
  static const double defaultPrivacyRadius = 100.0; // meters
  static const int maxPrivacyZones = 10;
  
  // Analytics
  static const int analyticsRetentionDays = 90;
  static const int productivityScoreMax = 100;
  
  // Chat
  static const int maxChatHistory = 100;
  static const int chatResponseTimeout = 30; // seconds
  
  // Background Service
  static const String backgroundTaskName = 'life_tracker_capture';
  static const int backgroundTaskFrequency = 15; // minutes
  
  // File Paths
  static const String imagesDirectory = 'life_tracker_images';
  static const String exportsDirectory = 'life_tracker_exports';
  
  // Permissions
  static const List<String> requiredPermissions = [
    'camera',
    'storage',
    'location',
    'microphone',
  ];
}

class ApiEndpoints {
  static const String uploadImage = '/upload';
  static const String analyzeImage = '/analyze';
  static const String getInsights = '/insights';
  static const String chat = '/chat';
  static const String getTimeline = '/timeline';
  static const String getAnalytics = '/analytics';
  static const String updateSettings = '/settings';
}

class AppColors {
  // Primary Colors
  static const int primaryBlue = 0xFF2196F3;
  static const int primaryGreen = 0xFF4CAF50;
  static const int primaryPurple = 0xFF9C27B0;
  
  // Neutral Colors
  static const int darkGrey = 0xFF424242;
  static const int lightGrey = 0xFFF5F5F5;
  static const int white = 0xFFFFFFFF;
  static const int black = 0xFF000000;
  
  // Status Colors
  static const int success = 0xFF4CAF50;
  static const int warning = 0xFFFF9800;
  static const int error = 0xFFF44336;
  static const int info = 0xFF2196F3;
}

class AppSizes {
  // Padding
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  
  // Border Radius
  static const double radiusS = 4.0;
  static const double radiusM = 8.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 24.0;
  
  // Icon Sizes
  static const double iconS = 16.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 48.0;
  
  // Button Heights
  static const double buttonHeight = 48.0;
  static const double buttonHeightS = 36.0;
  static const double buttonHeightL = 56.0;
}
