// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'image_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ImageModelAdapter extends TypeAdapter<ImageModel> {
  @override
  final int typeId = 0;

  @override
  ImageModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ImageModel(
      id: fields[0] as String,
      localPath: fields[1] as String,
      remotePath: fields[2] as String?,
      capturedAt: fields[3] as DateTime,
      latitude: fields[4] as double?,
      longitude: fields[5] as double?,
      aiAnalysis: fields[6] as String?,
      tags: (fields[7] as List).cast<String>(),
      isUploaded: fields[8] as bool,
      isAnalyzed: fields[9] as bool,
      captureType: fields[10] as CaptureType,
      fileSize: fields[11] as int,
      activityType: fields[12] as String?,
      productivityScore: fields[13] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, ImageModel obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.localPath)
      ..writeByte(2)
      ..write(obj.remotePath)
      ..writeByte(3)
      ..write(obj.capturedAt)
      ..writeByte(4)
      ..write(obj.latitude)
      ..writeByte(5)
      ..write(obj.longitude)
      ..writeByte(6)
      ..write(obj.aiAnalysis)
      ..writeByte(7)
      ..write(obj.tags)
      ..writeByte(8)
      ..write(obj.isUploaded)
      ..writeByte(9)
      ..write(obj.isAnalyzed)
      ..writeByte(10)
      ..write(obj.captureType)
      ..writeByte(11)
      ..write(obj.fileSize)
      ..writeByte(12)
      ..write(obj.activityType)
      ..writeByte(13)
      ..write(obj.productivityScore);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ImageModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AnalyticsDataAdapter extends TypeAdapter<AnalyticsData> {
  @override
  final int typeId = 2;

  @override
  AnalyticsData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AnalyticsData(
      date: fields[0] as DateTime,
      totalImages: fields[1] as int,
      averageProductivity: fields[2] as double,
      activityBreakdown: (fields[3] as Map).cast<String, int>(),
      topActivities: (fields[4] as List).cast<String>(),
      activeHours: fields[5] as int,
      focusScore: fields[6] as double,
    );
  }

  @override
  void write(BinaryWriter writer, AnalyticsData obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.date)
      ..writeByte(1)
      ..write(obj.totalImages)
      ..writeByte(2)
      ..write(obj.averageProductivity)
      ..writeByte(3)
      ..write(obj.activityBreakdown)
      ..writeByte(4)
      ..write(obj.topActivities)
      ..writeByte(5)
      ..write(obj.activeHours)
      ..writeByte(6)
      ..write(obj.focusScore);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AnalyticsDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ChatMessageAdapter extends TypeAdapter<ChatMessage> {
  @override
  final int typeId = 3;

  @override
  ChatMessage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ChatMessage(
      id: fields[0] as String,
      message: fields[1] as String,
      isUser: fields[2] as bool,
      timestamp: fields[3] as DateTime,
      relatedImageIds: (fields[4] as List?)?.cast<String>(),
      context: fields[5] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ChatMessage obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.message)
      ..writeByte(2)
      ..write(obj.isUser)
      ..writeByte(3)
      ..write(obj.timestamp)
      ..writeByte(4)
      ..write(obj.relatedImageIds)
      ..writeByte(5)
      ..write(obj.context);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChatMessageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PrivacyZoneAdapter extends TypeAdapter<PrivacyZone> {
  @override
  final int typeId = 4;

  @override
  PrivacyZone read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PrivacyZone(
      id: fields[0] as String,
      name: fields[1] as String,
      latitude: fields[2] as double,
      longitude: fields[3] as double,
      radius: fields[4] as double,
      isActive: fields[5] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, PrivacyZone obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.latitude)
      ..writeByte(3)
      ..write(obj.longitude)
      ..writeByte(4)
      ..write(obj.radius)
      ..writeByte(5)
      ..write(obj.isActive);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PrivacyZoneAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CaptureTypeAdapter extends TypeAdapter<CaptureType> {
  @override
  final int typeId = 1;

  @override
  CaptureType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CaptureType.manual;
      case 1:
        return CaptureType.automatic;
      case 2:
        return CaptureType.motionTriggered;
      case 3:
        return CaptureType.scheduled;
      default:
        return CaptureType.manual;
    }
  }

  @override
  void write(BinaryWriter writer, CaptureType obj) {
    switch (obj) {
      case CaptureType.manual:
        writer.writeByte(0);
        break;
      case CaptureType.automatic:
        writer.writeByte(1);
        break;
      case CaptureType.motionTriggered:
        writer.writeByte(2);
        break;
      case CaptureType.scheduled:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CaptureTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ImageModel _$ImageModelFromJson(Map<String, dynamic> json) => ImageModel(
      id: json['id'] as String,
      localPath: json['localPath'] as String,
      remotePath: json['remotePath'] as String?,
      capturedAt: DateTime.parse(json['capturedAt'] as String),
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      aiAnalysis: json['aiAnalysis'] as String?,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      isUploaded: json['isUploaded'] as bool? ?? false,
      isAnalyzed: json['isAnalyzed'] as bool? ?? false,
      captureType: $enumDecode(_$CaptureTypeEnumMap, json['captureType']),
      fileSize: (json['fileSize'] as num).toInt(),
      activityType: json['activityType'] as String?,
      productivityScore: (json['productivityScore'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$ImageModelToJson(ImageModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'localPath': instance.localPath,
      'remotePath': instance.remotePath,
      'capturedAt': instance.capturedAt.toIso8601String(),
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'aiAnalysis': instance.aiAnalysis,
      'tags': instance.tags,
      'isUploaded': instance.isUploaded,
      'isAnalyzed': instance.isAnalyzed,
      'captureType': _$CaptureTypeEnumMap[instance.captureType]!,
      'fileSize': instance.fileSize,
      'activityType': instance.activityType,
      'productivityScore': instance.productivityScore,
    };

const _$CaptureTypeEnumMap = {
  CaptureType.manual: 'manual',
  CaptureType.automatic: 'automatic',
  CaptureType.motionTriggered: 'motionTriggered',
  CaptureType.scheduled: 'scheduled',
};

AnalyticsData _$AnalyticsDataFromJson(Map<String, dynamic> json) =>
    AnalyticsData(
      date: DateTime.parse(json['date'] as String),
      totalImages: (json['totalImages'] as num).toInt(),
      averageProductivity: (json['averageProductivity'] as num).toDouble(),
      activityBreakdown:
          Map<String, int>.from(json['activityBreakdown'] as Map),
      topActivities: (json['topActivities'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      activeHours: (json['activeHours'] as num).toInt(),
      focusScore: (json['focusScore'] as num).toDouble(),
    );

Map<String, dynamic> _$AnalyticsDataToJson(AnalyticsData instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'totalImages': instance.totalImages,
      'averageProductivity': instance.averageProductivity,
      'activityBreakdown': instance.activityBreakdown,
      'topActivities': instance.topActivities,
      'activeHours': instance.activeHours,
      'focusScore': instance.focusScore,
    };

ChatMessage _$ChatMessageFromJson(Map<String, dynamic> json) => ChatMessage(
      id: json['id'] as String,
      message: json['message'] as String,
      isUser: json['isUser'] as bool,
      timestamp: DateTime.parse(json['timestamp'] as String),
      relatedImageIds: (json['relatedImageIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      context: json['context'] as String?,
    );

Map<String, dynamic> _$ChatMessageToJson(ChatMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'message': instance.message,
      'isUser': instance.isUser,
      'timestamp': instance.timestamp.toIso8601String(),
      'relatedImageIds': instance.relatedImageIds,
      'context': instance.context,
    };

PrivacyZone _$PrivacyZoneFromJson(Map<String, dynamic> json) => PrivacyZone(
      id: json['id'] as String,
      name: json['name'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      radius: (json['radius'] as num).toDouble(),
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$PrivacyZoneToJson(PrivacyZone instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'radius': instance.radius,
      'isActive': instance.isActive,
    };
