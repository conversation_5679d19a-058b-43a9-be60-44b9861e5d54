import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'image_model.g.dart';

@HiveType(typeId: 0)
@JsonSerializable()
class ImageModel {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String localPath;
  
  @HiveField(2)
  final String? remotePath;
  
  @HiveField(3)
  final DateTime capturedAt;
  
  @HiveField(4)
  final double? latitude;
  
  @HiveField(5)
  final double? longitude;
  
  @HiveField(6)
  final String? aiAnalysis;
  
  @HiveField(7)
  final List<String> tags;
  
  @HiveField(8)
  final bool isUploaded;
  
  @HiveField(9)
  final bool isAnalyzed;
  
  @HiveField(10)
  final CaptureType captureType;
  
  @HiveField(11)
  final int fileSize;
  
  @HiveField(12)
  final String? activityType;
  
  @HiveField(13)
  final double? productivityScore;

  const ImageModel({
    required this.id,
    required this.localPath,
    this.remotePath,
    required this.capturedAt,
    this.latitude,
    this.longitude,
    this.aiAnalysis,
    this.tags = const [],
    this.isUploaded = false,
    this.isAnalyzed = false,
    required this.captureType,
    required this.fileSize,
    this.activityType,
    this.productivityScore,
  });

  factory ImageModel.fromJson(Map<String, dynamic> json) => _$ImageModelFromJson(json);
  Map<String, dynamic> toJson() => _$ImageModelToJson(this);

  ImageModel copyWith({
    String? id,
    String? localPath,
    String? remotePath,
    DateTime? capturedAt,
    double? latitude,
    double? longitude,
    String? aiAnalysis,
    List<String>? tags,
    bool? isUploaded,
    bool? isAnalyzed,
    CaptureType? captureType,
    int? fileSize,
    String? activityType,
    double? productivityScore,
  }) {
    return ImageModel(
      id: id ?? this.id,
      localPath: localPath ?? this.localPath,
      remotePath: remotePath ?? this.remotePath,
      capturedAt: capturedAt ?? this.capturedAt,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      aiAnalysis: aiAnalysis ?? this.aiAnalysis,
      tags: tags ?? this.tags,
      isUploaded: isUploaded ?? this.isUploaded,
      isAnalyzed: isAnalyzed ?? this.isAnalyzed,
      captureType: captureType ?? this.captureType,
      fileSize: fileSize ?? this.fileSize,
      activityType: activityType ?? this.activityType,
      productivityScore: productivityScore ?? this.productivityScore,
    );
  }
}

@HiveType(typeId: 1)
enum CaptureType {
  @HiveField(0)
  manual,
  
  @HiveField(1)
  automatic,
  
  @HiveField(2)
  motionTriggered,
  
  @HiveField(3)
  scheduled,
}

@HiveType(typeId: 2)
@JsonSerializable()
class AnalyticsData {
  @HiveField(0)
  final DateTime date;
  
  @HiveField(1)
  final int totalImages;
  
  @HiveField(2)
  final double averageProductivity;
  
  @HiveField(3)
  final Map<String, int> activityBreakdown;
  
  @HiveField(4)
  final List<String> topActivities;
  
  @HiveField(5)
  final int activeHours;
  
  @HiveField(6)
  final double focusScore;

  const AnalyticsData({
    required this.date,
    required this.totalImages,
    required this.averageProductivity,
    required this.activityBreakdown,
    required this.topActivities,
    required this.activeHours,
    required this.focusScore,
  });

  factory AnalyticsData.fromJson(Map<String, dynamic> json) => _$AnalyticsDataFromJson(json);
  Map<String, dynamic> toJson() => _$AnalyticsDataToJson(this);
}

@HiveType(typeId: 3)
@JsonSerializable()
class ChatMessage {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String message;
  
  @HiveField(2)
  final bool isUser;
  
  @HiveField(3)
  final DateTime timestamp;
  
  @HiveField(4)
  final List<String>? relatedImageIds;
  
  @HiveField(5)
  final String? context;

  const ChatMessage({
    required this.id,
    required this.message,
    required this.isUser,
    required this.timestamp,
    this.relatedImageIds,
    this.context,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) => _$ChatMessageFromJson(json);
  Map<String, dynamic> toJson() => _$ChatMessageToJson(this);
}

@HiveType(typeId: 4)
@JsonSerializable()
class PrivacyZone {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String name;
  
  @HiveField(2)
  final double latitude;
  
  @HiveField(3)
  final double longitude;
  
  @HiveField(4)
  final double radius;
  
  @HiveField(5)
  final bool isActive;

  const PrivacyZone({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.radius,
    this.isActive = true,
  });

  factory PrivacyZone.fromJson(Map<String, dynamic> json) => _$PrivacyZoneFromJson(json);
  Map<String, dynamic> toJson() => _$PrivacyZoneToJson(this);
}
