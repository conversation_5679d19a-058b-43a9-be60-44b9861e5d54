import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:intl/intl.dart';
import '../../core/constants/app_constants.dart';
import '../../core/providers/app_providers.dart';
import '../../core/models/image_model.dart';
import 'camera_screen.dart';

class GalleryScreen extends ConsumerStatefulWidget {
  const GalleryScreen({super.key});

  @override
  ConsumerState<GalleryScreen> createState() => _GalleryScreenState();
}

class _GalleryScreenState extends ConsumerState<GalleryScreen> {
  bool _isSelectionMode = false;
  final Set<String> _selectedImages = {};
  String _sortBy = 'date';
  String _filterBy = 'all';

  @override
  Widget build(BuildContext context) {
    final images = ref.watch(imagesProvider);
    final filteredImages = _getFilteredImages(images);

    return Scaffold(
      appBar: AppBar(
        title: _isSelectionMode
            ? Text('${_selectedImages.length} selected')
            : const Text('Gallery'),
        leading: _isSelectionMode
            ? IconButton(
                icon: Icon(MdiIcons.close),
                onPressed: _exitSelectionMode,
              )
            : null,
        actions: [
          if (_isSelectionMode) ...[
            IconButton(
              icon: Icon(MdiIcons.selectAll),
              onPressed: _selectAll,
            ),
            IconButton(
              icon: Icon(MdiIcons.delete),
              onPressed: _selectedImages.isNotEmpty ? _deleteSelected : null,
            ),
          ] else ...[
            IconButton(
              icon: Icon(MdiIcons.sort),
              onPressed: _showSortOptions,
            ),
            IconButton(
              icon: Icon(MdiIcons.filterVariant),
              onPressed: _showFilterOptions,
            ),
          ],
        ],
      ),
      body: filteredImages.isEmpty
          ? _buildEmptyState()
          : GridView.builder(
              padding: const EdgeInsets.all(AppSizes.paddingS),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: AppSizes.paddingS,
                mainAxisSpacing: AppSizes.paddingS,
                childAspectRatio: 1,
              ),
              itemCount: filteredImages.length,
              itemBuilder: (context, index) {
                return _buildImageTile(filteredImages[index]);
              },
            ),
      floatingActionButton: _isSelectionMode
          ? null
          : FloatingActionButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CameraScreen(),
                  ),
                );
              },
              child: Icon(MdiIcons.camera),
            ),
    );
  }

  List<ImageModel> _getFilteredImages(List<ImageModel> images) {
    var filtered = images.where((image) {
      switch (_filterBy) {
        case 'analyzed':
          return image.isAnalyzed;
        case 'unanalyzed':
          return !image.isAnalyzed;
        case 'manual':
          return image.captureType == CaptureType.manual;
        case 'automatic':
          return image.captureType == CaptureType.automatic;
        default:
          return true;
      }
    }).toList();

    switch (_sortBy) {
      case 'date':
        filtered.sort((a, b) => b.capturedAt.compareTo(a.capturedAt));
        break;
      case 'size':
        filtered.sort((a, b) => b.fileSize.compareTo(a.fileSize));
        break;
      case 'type':
        filtered.sort((a, b) => a.captureType.name.compareTo(b.captureType.name));
        break;
    }

    return filtered;
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            MdiIcons.imageOff,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: AppSizes.paddingL),
          Text(
            'No images found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppSizes.paddingS),
          Text(
            'Start capturing to see your images here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageTile(ImageModel image) {
    final isSelected = _selectedImages.contains(image.id);

    return GestureDetector(
      onTap: () {
        if (_isSelectionMode) {
          _toggleSelection(image.id);
        } else {
          _viewImage(image);
        }
      },
      onLongPress: () {
        if (!_isSelectionMode) {
          _enterSelectionMode(image.id);
        }
      },
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(AppSizes.radiusM),
              border: isSelected
                  ? Border.all(
                      color: Theme.of(context).colorScheme.primary,
                      width: 3,
                    )
                  : null,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(AppSizes.radiusM),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // Image placeholder
                  Icon(
                    MdiIcons.image,
                    size: AppSizes.iconXL,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  
                  // Overlay with info
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(AppSizes.paddingXS),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withOpacity(0.7),
                          ],
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            DateFormat('HH:mm').format(image.capturedAt),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Row(
                            children: [
                              Icon(
                                _getCaptureTypeIcon(image.captureType),
                                color: Colors.white,
                                size: 12,
                              ),
                              const SizedBox(width: 2),
                              if (image.isAnalyzed)
                                Icon(
                                  MdiIcons.checkCircle,
                                  color: Colors.green,
                                  size: 12,
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Selection indicator
          if (_isSelectionMode)
            Positioned(
              top: AppSizes.paddingXS,
              right: AppSizes.paddingXS,
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Colors.white.withOpacity(0.7),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? Icon(
                        MdiIcons.check,
                        color: Theme.of(context).colorScheme.onPrimary,
                        size: 16,
                      )
                    : null,
              ),
            ),
        ],
      ),
    );
  }

  IconData _getCaptureTypeIcon(CaptureType type) {
    switch (type) {
      case CaptureType.manual:
        return MdiIcons.camera;
      case CaptureType.automatic:
        return MdiIcons.cameraTimer;
      case CaptureType.motionTriggered:
        return MdiIcons.motionSensor;
      case CaptureType.scheduled:
        return MdiIcons.cameraTimer;
    }
  }

  void _enterSelectionMode(String imageId) {
    setState(() {
      _isSelectionMode = true;
      _selectedImages.add(imageId);
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedImages.clear();
    });
  }

  void _toggleSelection(String imageId) {
    setState(() {
      if (_selectedImages.contains(imageId)) {
        _selectedImages.remove(imageId);
        if (_selectedImages.isEmpty) {
          _isSelectionMode = false;
        }
      } else {
        _selectedImages.add(imageId);
      }
    });
  }

  void _selectAll() {
    final images = ref.read(imagesProvider);
    setState(() {
      _selectedImages.addAll(images.map((image) => image.id));
    });
  }

  void _deleteSelected() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Images'),
        content: Text('Are you sure you want to delete ${_selectedImages.length} image(s)?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(imagesProvider.notifier).deleteMultipleImages(_selectedImages.toList());
              _exitSelectionMode();
              Navigator.pop(context);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _viewImage(ImageModel image) {
    // TODO: Navigate to image detail view
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: const Text('Sort by Date'),
            trailing: _sortBy == 'date' ? Icon(MdiIcons.check) : null,
            onTap: () {
              setState(() => _sortBy = 'date');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Sort by Size'),
            trailing: _sortBy == 'size' ? Icon(MdiIcons.check) : null,
            onTap: () {
              setState(() => _sortBy = 'size');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Sort by Type'),
            trailing: _sortBy == 'type' ? Icon(MdiIcons.check) : null,
            onTap: () {
              setState(() => _sortBy = 'type');
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: const Text('All Images'),
            trailing: _filterBy == 'all' ? Icon(MdiIcons.check) : null,
            onTap: () {
              setState(() => _filterBy = 'all');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Analyzed'),
            trailing: _filterBy == 'analyzed' ? Icon(MdiIcons.check) : null,
            onTap: () {
              setState(() => _filterBy = 'analyzed');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Unanalyzed'),
            trailing: _filterBy == 'unanalyzed' ? Icon(MdiIcons.check) : null,
            onTap: () {
              setState(() => _filterBy = 'unanalyzed');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Manual Captures'),
            trailing: _filterBy == 'manual' ? Icon(MdiIcons.check) : null,
            onTap: () {
              setState(() => _filterBy = 'manual');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Automatic Captures'),
            trailing: _filterBy == 'automatic' ? Icon(MdiIcons.check) : null,
            onTap: () {
              setState(() => _filterBy = 'automatic');
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }
}
