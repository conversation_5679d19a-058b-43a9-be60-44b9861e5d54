import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/background_service.dart';
import '../models/user_settings.dart';
import 'app_providers.dart';

// Background service provider
final backgroundServiceProvider = Provider<BackgroundService>((ref) {
  return BackgroundService.instance;
});

// Background service state provider
final backgroundServiceStateProvider = StateNotifierProvider<BackgroundServiceNotifier, BackgroundServiceState>((ref) {
  return BackgroundServiceNotifier(
    ref.read(backgroundServiceProvider),
    ref.read(userSettingsProvider),
    ref.read(appStateProvider.notifier),
  );
});

class BackgroundServiceState {
  final bool isRunning;
  final bool isForegroundCapturing;
  final bool isMotionDetectionActive;
  final DateTime? lastCaptureTime;
  final String? error;

  const BackgroundServiceState({
    this.isRunning = false,
    this.isForegroundCapturing = false,
    this.isMotionDetectionActive = false,
    this.lastCaptureTime,
    this.error,
  });

  BackgroundServiceState copyWith({
    bool? isRunning,
    bool? isForegroundCapturing,
    bool? isMotionDetectionActive,
    DateTime? lastCaptureTime,
    String? error,
  }) {
    return BackgroundServiceState(
      isRunning: isRunning ?? this.isRunning,
      isForegroundCapturing: isForegroundCapturing ?? this.isForegroundCapturing,
      isMotionDetectionActive: isMotionDetectionActive ?? this.isMotionDetectionActive,
      lastCaptureTime: lastCaptureTime ?? this.lastCaptureTime,
      error: error,
    );
  }
}

class BackgroundServiceNotifier extends StateNotifier<BackgroundServiceState> {
  final BackgroundService _backgroundService;
  final UserSettings _userSettings;
  final AppStateNotifier _appStateNotifier;

  BackgroundServiceNotifier(
    this._backgroundService,
    this._userSettings,
    this._appStateNotifier,
  ) : super(const BackgroundServiceState()) {
    _initializeService();
  }

  Future<void> _initializeService() async {
    try {
      await _backgroundService.initialize();
      
      // Update state based on current service status
      state = state.copyWith(
        isRunning: _backgroundService.isRunning,
        error: null,
      );
      
      // Update app state
      await _appStateNotifier.updateBackgroundServiceStatus(_backgroundService.isRunning);
    } catch (e) {
      state = state.copyWith(error: 'Failed to initialize background service: $e');
    }
  }

  Future<void> startBackgroundCapture() async {
    if (!_userSettings.isTrackingEnabled) {
      state = state.copyWith(error: 'Tracking is disabled');
      return;
    }

    try {
      state = state.copyWith(error: null);
      
      await _backgroundService.startBackgroundCapture();
      
      state = state.copyWith(
        isRunning: true,
        lastCaptureTime: DateTime.now(),
      );
      
      await _appStateNotifier.updateBackgroundServiceStatus(true);
    } catch (e) {
      state = state.copyWith(error: 'Failed to start background capture: $e');
    }
  }

  Future<void> stopBackgroundCapture() async {
    try {
      state = state.copyWith(error: null);
      
      await _backgroundService.stopBackgroundCapture();
      
      state = state.copyWith(isRunning: false);
      
      await _appStateNotifier.updateBackgroundServiceStatus(false);
    } catch (e) {
      state = state.copyWith(error: 'Failed to stop background capture: $e');
    }
  }

  Future<void> startForegroundCapture() async {
    if (!_userSettings.isTrackingEnabled) {
      state = state.copyWith(error: 'Tracking is disabled');
      return;
    }

    try {
      state = state.copyWith(error: null);
      
      await _backgroundService.startForegroundCapture(_userSettings);
      
      state = state.copyWith(
        isForegroundCapturing: true,
        lastCaptureTime: DateTime.now(),
      );
    } catch (e) {
      state = state.copyWith(error: 'Failed to start foreground capture: $e');
    }
  }

  Future<void> stopForegroundCapture() async {
    try {
      state = state.copyWith(error: null);
      
      await _backgroundService.stopForegroundCapture();
      
      state = state.copyWith(isForegroundCapturing: false);
    } catch (e) {
      state = state.copyWith(error: 'Failed to stop foreground capture: $e');
    }
  }

  Future<void> startMotionDetection() async {
    if (!_userSettings.motionDetectionEnabled) {
      state = state.copyWith(error: 'Motion detection is disabled');
      return;
    }

    try {
      state = state.copyWith(error: null);
      
      await MotionDetectionService.instance.startMotionDetection();
      
      state = state.copyWith(isMotionDetectionActive: true);
    } catch (e) {
      state = state.copyWith(error: 'Failed to start motion detection: $e');
    }
  }

  Future<void> stopMotionDetection() async {
    try {
      state = state.copyWith(error: null);
      
      await MotionDetectionService.instance.stopMotionDetection();
      
      state = state.copyWith(isMotionDetectionActive: false);
    } catch (e) {
      state = state.copyWith(error: 'Failed to stop motion detection: $e');
    }
  }

  Future<void> toggleBackgroundService() async {
    if (state.isRunning) {
      await stopBackgroundCapture();
    } else {
      await startBackgroundCapture();
    }
  }

  Future<void> toggleForegroundCapture() async {
    if (state.isForegroundCapturing) {
      await stopForegroundCapture();
    } else {
      await startForegroundCapture();
    }
  }

  Future<void> toggleMotionDetection() async {
    if (state.isMotionDetectionActive) {
      await stopMotionDetection();
    } else {
      await startMotionDetection();
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void updateLastCaptureTime() {
    state = state.copyWith(lastCaptureTime: DateTime.now());
  }
}

// Motion detection provider
final motionDetectionProvider = Provider<MotionDetectionService>((ref) {
  return MotionDetectionService.instance;
});

// Combined automation provider that manages all automated features
final automationProvider = StateNotifierProvider<AutomationNotifier, AutomationState>((ref) {
  return AutomationNotifier(
    ref.read(backgroundServiceStateProvider.notifier),
    ref.read(userSettingsProvider),
  );
});

class AutomationState {
  final bool isFullyAutomated;
  final bool hasActiveServices;
  final List<String> activeServices;
  final String? status;

  const AutomationState({
    this.isFullyAutomated = false,
    this.hasActiveServices = false,
    this.activeServices = const [],
    this.status,
  });

  AutomationState copyWith({
    bool? isFullyAutomated,
    bool? hasActiveServices,
    List<String>? activeServices,
    String? status,
  }) {
    return AutomationState(
      isFullyAutomated: isFullyAutomated ?? this.isFullyAutomated,
      hasActiveServices: hasActiveServices ?? this.hasActiveServices,
      activeServices: activeServices ?? this.activeServices,
      status: status ?? this.status,
    );
  }
}

class AutomationNotifier extends StateNotifier<AutomationState> {
  final BackgroundServiceNotifier _backgroundServiceNotifier;
  final UserSettings _userSettings;

  AutomationNotifier(
    this._backgroundServiceNotifier,
    this._userSettings,
  ) : super(const AutomationState()) {
    _updateAutomationState();
  }

  void _updateAutomationState() {
    final activeServices = <String>[];
    
    if (_userSettings.isTrackingEnabled) {
      activeServices.add('Tracking');
    }
    
    if (_userSettings.motionDetectionEnabled) {
      activeServices.add('Motion Detection');
    }
    
    final isFullyAutomated = _userSettings.isTrackingEnabled && 
                            _userSettings.motionDetectionEnabled;
    
    state = state.copyWith(
      isFullyAutomated: isFullyAutomated,
      hasActiveServices: activeServices.isNotEmpty,
      activeServices: activeServices,
      status: _getStatusMessage(activeServices),
    );
  }

  String _getStatusMessage(List<String> activeServices) {
    if (activeServices.isEmpty) {
      return 'All automation disabled';
    } else if (activeServices.length == 1) {
      return '${activeServices.first} active';
    } else {
      return '${activeServices.length} services active';
    }
  }

  Future<void> enableFullAutomation() async {
    await _backgroundServiceNotifier.startBackgroundCapture();
    await _backgroundServiceNotifier.startMotionDetection();
    _updateAutomationState();
  }

  Future<void> disableAllAutomation() async {
    await _backgroundServiceNotifier.stopBackgroundCapture();
    await _backgroundServiceNotifier.stopForegroundCapture();
    await _backgroundServiceNotifier.stopMotionDetection();
    _updateAutomationState();
  }
}
