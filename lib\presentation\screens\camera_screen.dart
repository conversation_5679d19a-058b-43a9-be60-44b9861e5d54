import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:camera/camera.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../core/constants/app_constants.dart';
import '../../core/providers/camera_provider.dart';
import '../../core/models/image_model.dart';

class CameraScreen extends ConsumerStatefulWidget {
  const CameraScreen({super.key});

  @override
  ConsumerState<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends ConsumerState<CameraScreen>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(cameraProvider.notifier).initialize();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final cameraNotifier = ref.read(cameraProvider.notifier);
    
    if (state == AppLifecycleState.inactive) {
      cameraNotifier.dispose();
    } else if (state == AppLifecycleState.resumed) {
      cameraNotifier.initialize();
    }
  }

  @override
  Widget build(BuildContext context) {
    final cameraState = ref.watch(cameraProvider);
    final cameraService = ref.watch(cameraServiceProvider);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          if (cameraState.isInitialized && cameraService.hasMultipleCameras)
            IconButton(
              icon: Icon(MdiIcons.cameraSwitch, color: Colors.white),
              onPressed: () {
                ref.read(cameraProvider.notifier).switchCamera();
              },
            ),
        ],
      ),
      body: cameraState.isInitialized
          ? _buildCameraPreview(context, cameraState, cameraService)
          : _buildLoadingView(context, cameraState),
    );
  }

  Widget _buildLoadingView(BuildContext context, CameraState cameraState) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (cameraState.error != null) ...[
            Icon(
              MdiIcons.cameraOff,
              size: 64,
              color: Colors.white70,
            ),
            const SizedBox(height: AppSizes.paddingL),
            Text(
              'Camera Error',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.white,
              ),
            ),
            const SizedBox(height: AppSizes.paddingM),
            Text(
              cameraState.error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSizes.paddingL),
            ElevatedButton(
              onPressed: () {
                ref.read(cameraProvider.notifier).initialize();
              },
              child: const Text('Retry'),
            ),
          ] else ...[
            const CircularProgressIndicator(color: Colors.white),
            const SizedBox(height: AppSizes.paddingL),
            Text(
              'Initializing Camera...',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCameraPreview(
    BuildContext context,
    CameraState cameraState,
    CameraService cameraService,
  ) {
    return Stack(
      children: [
        // Camera preview
        Positioned.fill(
          child: GestureDetector(
            onTapUp: (details) {
              final RenderBox box = context.findRenderObject() as RenderBox;
              final Offset localPoint = box.globalToLocal(details.globalPosition);
              final Offset relativePoint = Offset(
                localPoint.dx / box.size.width,
                localPoint.dy / box.size.height,
              );
              ref.read(cameraProvider.notifier).focusOnPoint(relativePoint);
            },
            child: CameraPreview(cameraService.controller!),
          ),
        ),

        // Camera controls overlay
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: _buildCameraControls(context, cameraState),
        ),

        // Top controls
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: _buildTopControls(context, cameraState),
        ),

        // Error overlay
        if (cameraState.error != null)
          Positioned(
            top: 100,
            left: AppSizes.paddingM,
            right: AppSizes.paddingM,
            child: Container(
              padding: const EdgeInsets.all(AppSizes.paddingM),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.8),
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
              child: Row(
                children: [
                  Icon(MdiIcons.alert, color: Colors.white),
                  const SizedBox(width: AppSizes.paddingS),
                  Expanded(
                    child: Text(
                      cameraState.error!,
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                  IconButton(
                    icon: Icon(MdiIcons.close, color: Colors.white),
                    onPressed: () {
                      ref.read(cameraProvider.notifier).clearError();
                    },
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTopControls(BuildContext context, CameraState cameraState) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      child: Row(
        children: [
          // Flash mode button
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.5),
              borderRadius: BorderRadius.circular(AppSizes.radiusL),
            ),
            child: IconButton(
              icon: Icon(
                _getFlashIcon(cameraState.flashMode),
                color: Colors.white,
              ),
              onPressed: () => _showFlashModeDialog(context),
            ),
          ),
          const Spacer(),
          // Zoom level indicator
          if (cameraState.zoomLevel > 1.0)
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSizes.paddingM,
                vertical: AppSizes.paddingS,
              ),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(AppSizes.radiusL),
              ),
              child: Text(
                '${cameraState.zoomLevel.toStringAsFixed(1)}x',
                style: const TextStyle(color: Colors.white),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCameraControls(BuildContext context, CameraState cameraState) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withOpacity(0.7),
          ],
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Gallery button
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppSizes.radiusL),
            ),
            child: IconButton(
              icon: Icon(MdiIcons.imageMultiple, color: Colors.white),
              onPressed: () {
                Navigator.pop(context);
                // Navigate to gallery
              },
            ),
          ),

          // Capture button
          GestureDetector(
            onTap: cameraState.isCapturing ? null : _captureImage,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 4),
              ),
              child: cameraState.isCapturing
                  ? const CircularProgressIndicator(
                      strokeWidth: 3,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                    )
                  : Icon(
                      MdiIcons.camera,
                      size: 32,
                      color: Colors.black,
                    ),
            ),
          ),

          // Settings button
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppSizes.radiusL),
            ),
            child: IconButton(
              icon: Icon(MdiIcons.cog, color: Colors.white),
              onPressed: () => _showCameraSettings(context),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getFlashIcon(FlashMode flashMode) {
    switch (flashMode) {
      case FlashMode.auto:
        return MdiIcons.flashAuto;
      case FlashMode.always:
        return MdiIcons.flash;
      case FlashMode.off:
        return MdiIcons.flashOff;
      case FlashMode.torch:
        return MdiIcons.flashlight;
    }
  }

  void _captureImage() async {
    final ImageModel? image = await ref.read(cameraProvider.notifier).captureImage();
    
    if (image != null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Image captured successfully!'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _showFlashModeDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black87,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: Icon(MdiIcons.flashAuto, color: Colors.white),
            title: const Text('Auto', style: TextStyle(color: Colors.white)),
            onTap: () {
              ref.read(cameraProvider.notifier).setFlashMode(FlashMode.auto);
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: Icon(MdiIcons.flash, color: Colors.white),
            title: const Text('On', style: TextStyle(color: Colors.white)),
            onTap: () {
              ref.read(cameraProvider.notifier).setFlashMode(FlashMode.always);
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: Icon(MdiIcons.flashOff, color: Colors.white),
            title: const Text('Off', style: TextStyle(color: Colors.white)),
            onTap: () {
              ref.read(cameraProvider.notifier).setFlashMode(FlashMode.off);
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  void _showCameraSettings(BuildContext context) {
    // TODO: Implement camera settings dialog
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black87,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Camera Settings',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.white,
              ),
            ),
            const SizedBox(height: AppSizes.paddingL),
            const Text(
              'Camera settings will be implemented here',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: AppSizes.paddingL),
          ],
        ),
      ),
    );
  }
}
