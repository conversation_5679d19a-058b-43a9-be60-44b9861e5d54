import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:intl/intl.dart';
import '../../core/constants/app_constants.dart';
import '../../core/providers/app_providers.dart';
import '../../core/models/image_model.dart';

class TimelineScreen extends ConsumerStatefulWidget {
  const TimelineScreen({super.key});

  @override
  ConsumerState<TimelineScreen> createState() => _TimelineScreenState();
}

class _TimelineScreenState extends ConsumerState<TimelineScreen> {
  DateTime _selectedDate = DateTime.now();

  @override
  Widget build(BuildContext context) {
    final images = ref.watch(imagesProvider);
    final filteredImages = _getImagesForDate(_selectedDate, images);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Timeline'),
        actions: [
          IconButton(
            icon: Icon(MdiIcons.calendar),
            onPressed: () => _selectDate(context),
          ),
          IconButton(
            icon: Icon(MdiIcons.refresh),
            onPressed: () {
              ref.read(imagesProvider.notifier).refreshImages();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Date selector
          Container(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            child: Row(
              children: [
                IconButton(
                  icon: Icon(MdiIcons.chevronLeft),
                  onPressed: () {
                    setState(() {
                      _selectedDate = _selectedDate.subtract(const Duration(days: 1));
                    });
                  },
                ),
                Expanded(
                  child: Center(
                    child: Text(
                      DateFormat('EEEE, MMMM d, y').format(_selectedDate),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(MdiIcons.chevronRight),
                  onPressed: _selectedDate.isBefore(DateTime.now()) ? () {
                    setState(() {
                      _selectedDate = _selectedDate.add(const Duration(days: 1));
                    });
                  } : null,
                ),
              ],
            ),
          ),
          
          // Timeline content
          Expanded(
            child: filteredImages.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.all(AppSizes.paddingM),
                    itemCount: filteredImages.length,
                    itemBuilder: (context, index) {
                      return _buildTimelineItem(filteredImages[index], index);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  List<ImageModel> _getImagesForDate(DateTime date, List<ImageModel> allImages) {
    return allImages.where((image) {
      final imageDate = DateTime(
        image.capturedAt.year,
        image.capturedAt.month,
        image.capturedAt.day,
      );
      final targetDate = DateTime(date.year, date.month, date.day);
      return imageDate.isAtSameMomentAs(targetDate);
    }).toList();
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            MdiIcons.timelineOutline,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: AppSizes.paddingL),
          Text(
            'No activities for this day',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppSizes.paddingS),
          Text(
            'Start tracking to see your daily timeline',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineItem(ImageModel image, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline indicator
          Column(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  shape: BoxShape.circle,
                ),
              ),
              if (index < 10) // Show line for first few items
                Container(
                  width: 2,
                  height: 60,
                  color: Theme.of(context).colorScheme.outline,
                ),
            ],
          ),
          const SizedBox(width: AppSizes.paddingM),
          
          // Content
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          DateFormat('HH:mm').format(image.capturedAt),
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        const Spacer(),
                        _buildCaptureTypeChip(image.captureType),
                      ],
                    ),
                    const SizedBox(height: AppSizes.paddingS),
                    
                    // Image placeholder
                    Container(
                      width: double.infinity,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(AppSizes.radiusM),
                      ),
                      child: Icon(
                        MdiIcons.image,
                        size: AppSizes.iconXL,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    
                    if (image.aiAnalysis != null) ...[
                      const SizedBox(height: AppSizes.paddingM),
                      Text(
                        image.aiAnalysis!,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                    
                    if (image.tags.isNotEmpty) ...[
                      const SizedBox(height: AppSizes.paddingS),
                      Wrap(
                        spacing: AppSizes.paddingS,
                        children: image.tags.map((tag) => Chip(
                          label: Text(tag),
                          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        )).toList(),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCaptureTypeChip(CaptureType type) {
    IconData icon;
    String label;
    
    switch (type) {
      case CaptureType.manual:
        icon = MdiIcons.camera;
        label = 'Manual';
        break;
      case CaptureType.automatic:
        icon = MdiIcons.cameraTimer;
        label = 'Auto';
        break;
      case CaptureType.motionTriggered:
        icon = MdiIcons.motionSensor;
        label = 'Motion';
        break;
      case CaptureType.scheduled:
        icon = MdiIcons.cameraTimer;
        label = 'Scheduled';
        break;
    }

    return Chip(
      avatar: Icon(icon, size: AppSizes.iconS),
      label: Text(label),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
    );
    
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }
}
