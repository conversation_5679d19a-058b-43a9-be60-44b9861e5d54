// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_settings.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserSettingsAdapter extends TypeAdapter<UserSettings> {
  @override
  final int typeId = 5;

  @override
  UserSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserSettings(
      isTrackingEnabled: fields[0] as bool,
      captureInterval: fields[1] as int,
      motionDetectionEnabled: fields[2] as bool,
      locationTrackingEnabled: fields[3] as bool,
      privacyZones: (fields[4] as List).cast<PrivacyZone>(),
      darkModeEnabled: fields[5] as bool,
      notificationsEnabled: fields[6] as bool,
      imageQuality: fields[7] as int,
      maxStorageImages: fields[8] as int,
      autoDeleteOldImages: fields[9] as bool,
      dataRetentionDays: fields[10] as int,
      preferredAiModel: fields[11] as String,
      voiceInputEnabled: fields[12] as bool,
      analyticsEnabled: fields[13] as bool,
      activeHours: (fields[14] as List).cast<String>(),
      batteryOptimizationEnabled: fields[15] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, UserSettings obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.isTrackingEnabled)
      ..writeByte(1)
      ..write(obj.captureInterval)
      ..writeByte(2)
      ..write(obj.motionDetectionEnabled)
      ..writeByte(3)
      ..write(obj.locationTrackingEnabled)
      ..writeByte(4)
      ..write(obj.privacyZones)
      ..writeByte(5)
      ..write(obj.darkModeEnabled)
      ..writeByte(6)
      ..write(obj.notificationsEnabled)
      ..writeByte(7)
      ..write(obj.imageQuality)
      ..writeByte(8)
      ..write(obj.maxStorageImages)
      ..writeByte(9)
      ..write(obj.autoDeleteOldImages)
      ..writeByte(10)
      ..write(obj.dataRetentionDays)
      ..writeByte(11)
      ..write(obj.preferredAiModel)
      ..writeByte(12)
      ..write(obj.voiceInputEnabled)
      ..writeByte(13)
      ..write(obj.analyticsEnabled)
      ..writeByte(14)
      ..write(obj.activeHours)
      ..writeByte(15)
      ..write(obj.batteryOptimizationEnabled);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AppStateAdapter extends TypeAdapter<AppState> {
  @override
  final int typeId = 6;

  @override
  AppState read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AppState(
      isFirstLaunch: fields[0] as bool,
      hasCompletedOnboarding: fields[1] as bool,
      lastSyncTime: fields[2] as DateTime?,
      isBackgroundServiceRunning: fields[3] as bool,
      totalImagesCount: fields[4] as int,
      unanalyzedImagesCount: fields[5] as int,
      lastError: fields[6] as String?,
      lastErrorTime: fields[7] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, AppState obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.isFirstLaunch)
      ..writeByte(1)
      ..write(obj.hasCompletedOnboarding)
      ..writeByte(2)
      ..write(obj.lastSyncTime)
      ..writeByte(3)
      ..write(obj.isBackgroundServiceRunning)
      ..writeByte(4)
      ..write(obj.totalImagesCount)
      ..writeByte(5)
      ..write(obj.unanalyzedImagesCount)
      ..writeByte(6)
      ..write(obj.lastError)
      ..writeByte(7)
      ..write(obj.lastErrorTime);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AppStateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserSettings _$UserSettingsFromJson(Map<String, dynamic> json) => UserSettings(
      isTrackingEnabled: json['isTrackingEnabled'] as bool? ?? false,
      captureInterval: (json['captureInterval'] as num?)?.toInt() ?? 300,
      motionDetectionEnabled: json['motionDetectionEnabled'] as bool? ?? true,
      locationTrackingEnabled:
          json['locationTrackingEnabled'] as bool? ?? false,
      privacyZones: (json['privacyZones'] as List<dynamic>?)
              ?.map((e) => PrivacyZone.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      darkModeEnabled: json['darkModeEnabled'] as bool? ?? false,
      notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
      imageQuality: (json['imageQuality'] as num?)?.toInt() ?? 85,
      maxStorageImages: (json['maxStorageImages'] as num?)?.toInt() ?? 1000,
      autoDeleteOldImages: json['autoDeleteOldImages'] as bool? ?? true,
      dataRetentionDays: (json['dataRetentionDays'] as num?)?.toInt() ?? 90,
      preferredAiModel: json['preferredAiModel'] as String? ?? 'gemini',
      voiceInputEnabled: json['voiceInputEnabled'] as bool? ?? true,
      analyticsEnabled: json['analyticsEnabled'] as bool? ?? true,
      activeHours: (json['activeHours'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const ["06:00", "22:00"],
      batteryOptimizationEnabled:
          json['batteryOptimizationEnabled'] as bool? ?? true,
    );

Map<String, dynamic> _$UserSettingsToJson(UserSettings instance) =>
    <String, dynamic>{
      'isTrackingEnabled': instance.isTrackingEnabled,
      'captureInterval': instance.captureInterval,
      'motionDetectionEnabled': instance.motionDetectionEnabled,
      'locationTrackingEnabled': instance.locationTrackingEnabled,
      'privacyZones': instance.privacyZones,
      'darkModeEnabled': instance.darkModeEnabled,
      'notificationsEnabled': instance.notificationsEnabled,
      'imageQuality': instance.imageQuality,
      'maxStorageImages': instance.maxStorageImages,
      'autoDeleteOldImages': instance.autoDeleteOldImages,
      'dataRetentionDays': instance.dataRetentionDays,
      'preferredAiModel': instance.preferredAiModel,
      'voiceInputEnabled': instance.voiceInputEnabled,
      'analyticsEnabled': instance.analyticsEnabled,
      'activeHours': instance.activeHours,
      'batteryOptimizationEnabled': instance.batteryOptimizationEnabled,
    };

AppState _$AppStateFromJson(Map<String, dynamic> json) => AppState(
      isFirstLaunch: json['isFirstLaunch'] as bool? ?? true,
      hasCompletedOnboarding: json['hasCompletedOnboarding'] as bool? ?? false,
      lastSyncTime: json['lastSyncTime'] == null
          ? null
          : DateTime.parse(json['lastSyncTime'] as String),
      isBackgroundServiceRunning:
          json['isBackgroundServiceRunning'] as bool? ?? false,
      totalImagesCount: (json['totalImagesCount'] as num?)?.toInt() ?? 0,
      unanalyzedImagesCount:
          (json['unanalyzedImagesCount'] as num?)?.toInt() ?? 0,
      lastError: json['lastError'] as String?,
      lastErrorTime: json['lastErrorTime'] == null
          ? null
          : DateTime.parse(json['lastErrorTime'] as String),
    );

Map<String, dynamic> _$AppStateToJson(AppState instance) => <String, dynamic>{
      'isFirstLaunch': instance.isFirstLaunch,
      'hasCompletedOnboarding': instance.hasCompletedOnboarding,
      'lastSyncTime': instance.lastSyncTime?.toIso8601String(),
      'isBackgroundServiceRunning': instance.isBackgroundServiceRunning,
      'totalImagesCount': instance.totalImagesCount,
      'unanalyzedImagesCount': instance.unanalyzedImagesCount,
      'lastError': instance.lastError,
      'lastErrorTime': instance.lastErrorTime?.toIso8601String(),
    };
