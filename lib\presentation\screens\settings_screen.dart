import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../core/constants/app_constants.dart';
import '../../core/providers/app_providers.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userSettings = ref.watch(userSettingsProvider);
    final appState = ref.watch(appStateProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        children: [
          // Tracking Settings
          _buildSection(
            context,
            'Tracking',
            [
              SwitchListTile(
                title: const Text('Enable Tracking'),
                subtitle: const Text('Automatically capture and analyze activities'),
                value: userSettings.isTrackingEnabled,
                onChanged: (value) {
                  ref.read(userSettingsProvider.notifier).toggleTracking();
                },
                secondary: Icon(MdiIcons.recordCircle),
              ),
              ListTile(
                title: const Text('Capture Interval'),
                subtitle: Text('${userSettings.captureInterval ~/ 60} minutes'),
                leading: Icon(MdiIcons.timer),
                onTap: () => _showCaptureIntervalDialog(context, ref),
              ),
              SwitchListTile(
                title: const Text('Motion Detection'),
                subtitle: const Text('Trigger capture on movement'),
                value: userSettings.motionDetectionEnabled,
                onChanged: (value) {
                  ref.read(userSettingsProvider.notifier).toggleMotionDetection();
                },
                secondary: Icon(MdiIcons.motionSensor),
              ),
            ],
          ),

          // Privacy Settings
          _buildSection(
            context,
            'Privacy',
            [
              SwitchListTile(
                title: const Text('Location Tracking'),
                subtitle: const Text('Include location data with captures'),
                value: userSettings.locationTrackingEnabled,
                onChanged: (value) {
                  ref.read(userSettingsProvider.notifier).toggleLocationTracking();
                },
                secondary: Icon(MdiIcons.mapMarker),
              ),
              ListTile(
                title: const Text('Privacy Zones'),
                subtitle: Text('${userSettings.privacyZones.length} zones configured'),
                leading: Icon(MdiIcons.shieldAccount),
                onTap: () {
                  // TODO: Navigate to privacy zones screen
                },
              ),
              ListTile(
                title: const Text('Data Retention'),
                subtitle: Text('${userSettings.dataRetentionDays} days'),
                leading: Icon(MdiIcons.database),
                onTap: () => _showDataRetentionDialog(context, ref),
              ),
            ],
          ),

          // AI Settings
          _buildSection(
            context,
            'AI & Analysis',
            [
              ListTile(
                title: const Text('AI Model'),
                subtitle: Text(userSettings.preferredAiModel.toUpperCase()),
                leading: Icon(MdiIcons.brain),
                onTap: () => _showAiModelDialog(context, ref),
              ),
              SwitchListTile(
                title: const Text('Voice Input'),
                subtitle: const Text('Enable voice commands in chat'),
                value: userSettings.voiceInputEnabled,
                onChanged: (value) {
                  // TODO: Update voice input setting
                },
                secondary: Icon(MdiIcons.microphone),
              ),
              SwitchListTile(
                title: const Text('Analytics'),
                subtitle: const Text('Generate productivity insights'),
                value: userSettings.analyticsEnabled,
                onChanged: (value) {
                  // TODO: Update analytics setting
                },
                secondary: Icon(MdiIcons.chartLine),
              ),
            ],
          ),

          // Storage Settings
          _buildSection(
            context,
            'Storage',
            [
              ListTile(
                title: const Text('Image Quality'),
                subtitle: Text('${userSettings.imageQuality}%'),
                leading: Icon(MdiIcons.imageFilterHdr),
                onTap: () => _showImageQualityDialog(context, ref),
              ),
              ListTile(
                title: const Text('Storage Limit'),
                subtitle: Text('${userSettings.maxStorageImages} images'),
                leading: Icon(MdiIcons.harddisk),
                onTap: () => _showStorageLimitDialog(context, ref),
              ),
              SwitchListTile(
                title: const Text('Auto-delete Old Images'),
                subtitle: const Text('Remove images after retention period'),
                value: userSettings.autoDeleteOldImages,
                onChanged: (value) {
                  // TODO: Update auto-delete setting
                },
                secondary: Icon(MdiIcons.deleteEmpty),
              ),
            ],
          ),

          // App Settings
          _buildSection(
            context,
            'App',
            [
              SwitchListTile(
                title: const Text('Dark Mode'),
                subtitle: const Text('Use dark theme'),
                value: userSettings.darkModeEnabled,
                onChanged: (value) {
                  ref.read(userSettingsProvider.notifier).toggleDarkMode();
                },
                secondary: Icon(MdiIcons.themeLightDark),
              ),
              SwitchListTile(
                title: const Text('Notifications'),
                subtitle: const Text('Show app notifications'),
                value: userSettings.notificationsEnabled,
                onChanged: (value) {
                  // TODO: Update notifications setting
                },
                secondary: Icon(MdiIcons.bell),
              ),
              SwitchListTile(
                title: const Text('Battery Optimization'),
                subtitle: const Text('Reduce background activity'),
                value: userSettings.batteryOptimizationEnabled,
                onChanged: (value) {
                  // TODO: Update battery optimization setting
                },
                secondary: Icon(MdiIcons.batteryHeart),
              ),
            ],
          ),

          // Data Management
          _buildSection(
            context,
            'Data',
            [
              ListTile(
                title: const Text('Export Data'),
                subtitle: const Text('Export your data and analytics'),
                leading: Icon(MdiIcons.export),
                onTap: () {
                  // TODO: Implement data export
                },
              ),
              ListTile(
                title: const Text('Clear Cache'),
                subtitle: const Text('Free up storage space'),
                leading: Icon(MdiIcons.broom),
                onTap: () => _showClearCacheDialog(context, ref),
              ),
              ListTile(
                title: const Text('Reset App'),
                subtitle: const Text('Clear all data and settings'),
                leading: Icon(MdiIcons.restore, color: Colors.red),
                onTap: () => _showResetAppDialog(context, ref),
              ),
            ],
          ),

          // About
          _buildSection(
            context,
            'About',
            [
              ListTile(
                title: const Text('Version'),
                subtitle: Text(AppConstants.appVersion),
                leading: Icon(MdiIcons.information),
              ),
              ListTile(
                title: const Text('Privacy Policy'),
                leading: Icon(MdiIcons.shield),
                onTap: () {
                  // TODO: Show privacy policy
                },
              ),
              ListTile(
                title: const Text('Terms of Service'),
                leading: Icon(MdiIcons.fileDocument),
                onTap: () {
                  // TODO: Show terms of service
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSizes.paddingM,
            vertical: AppSizes.paddingS,
          ),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        Card(
          child: Column(children: children),
        ),
        const SizedBox(height: AppSizes.paddingM),
      ],
    );
  }

  void _showCaptureIntervalDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Capture Interval'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('1 minute'),
              onTap: () {
                ref.read(userSettingsProvider.notifier).updateCaptureInterval(60);
                Navigator.pop(context);
              },
            ),
            ListTile(
              title: const Text('5 minutes'),
              onTap: () {
                ref.read(userSettingsProvider.notifier).updateCaptureInterval(300);
                Navigator.pop(context);
              },
            ),
            ListTile(
              title: const Text('10 minutes'),
              onTap: () {
                ref.read(userSettingsProvider.notifier).updateCaptureInterval(600);
                Navigator.pop(context);
              },
            ),
            ListTile(
              title: const Text('30 minutes'),
              onTap: () {
                ref.read(userSettingsProvider.notifier).updateCaptureInterval(1800);
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showDataRetentionDialog(BuildContext context, WidgetRef ref) {
    // TODO: Implement data retention dialog
  }

  void _showAiModelDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('AI Model'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Gemini'),
              subtitle: const Text('Google\'s AI model'),
              onTap: () {
                ref.read(userSettingsProvider.notifier).updatePreferredAiModel('gemini');
                Navigator.pop(context);
              },
            ),
            ListTile(
              title: const Text('Ollama'),
              subtitle: const Text('Local AI model'),
              onTap: () {
                ref.read(userSettingsProvider.notifier).updatePreferredAiModel('ollama');
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showImageQualityDialog(BuildContext context, WidgetRef ref) {
    // TODO: Implement image quality dialog
  }

  void _showStorageLimitDialog(BuildContext context, WidgetRef ref) {
    // TODO: Implement storage limit dialog
  }

  void _showClearCacheDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text('This will clear temporary files and free up storage space.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // TODO: Implement cache clearing
              Navigator.pop(context);
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _showResetAppDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset App'),
        content: const Text('This will permanently delete all your data and settings. This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // TODO: Implement app reset
              Navigator.pop(context);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
