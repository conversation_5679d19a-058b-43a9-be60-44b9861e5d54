import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:intl/intl.dart';
import '../../core/constants/app_constants.dart';
import '../../core/models/image_model.dart';

class RecentActivityCard extends StatelessWidget {
  final List<ImageModel> images;

  const RecentActivityCard({
    super.key,
    required this.images,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Recent Activity',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // TODO: Navigate to timeline
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: AppSizes.paddingM),
            if (images.isEmpty)
              _buildEmptyState(context)
            else
              Column(
                children: images.map((image) => _buildActivityItem(context, image)).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      child: Column(
        children: [
          Icon(
            MdiIcons.imageOff,
            size: AppSizes.iconXL,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: AppSizes.paddingM),
          Text(
            'No recent activity',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppSizes.paddingS),
          Text(
            'Start tracking to see your activities here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(BuildContext context, ImageModel image) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Row(
        children: [
          // Image thumbnail placeholder
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(AppSizes.radiusM),
            ),
            child: Icon(
              _getCaptureTypeIcon(image.captureType),
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(width: AppSizes.paddingM),
          
          // Activity details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  image.aiAnalysis ?? 'Image captured',
                  style: Theme.of(context).textTheme.bodyMedium,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: AppSizes.paddingXS),
                Row(
                  children: [
                    Icon(
                      MdiIcons.clock,
                      size: AppSizes.iconS,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: AppSizes.paddingXS),
                    Text(
                      DateFormat('HH:mm').format(image.capturedAt),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    if (image.activityType != null) ...[
                      const SizedBox(width: AppSizes.paddingS),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingS,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(AppSizes.radiusS),
                        ),
                        child: Text(
                          image.activityType!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
          
          // Status indicators
          Column(
            children: [
              if (image.isAnalyzed)
                Icon(
                  MdiIcons.checkCircle,
                  size: AppSizes.iconS,
                  color: Colors.green,
                )
              else
                Icon(
                  MdiIcons.clockAlert,
                  size: AppSizes.iconS,
                  color: Colors.orange,
                ),
              if (image.productivityScore != null) ...[
                const SizedBox(height: AppSizes.paddingXS),
                Text(
                  '${image.productivityScore!.round()}%',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: _getProductivityColor(image.productivityScore!),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  IconData _getCaptureTypeIcon(CaptureType type) {
    switch (type) {
      case CaptureType.manual:
        return MdiIcons.camera;
      case CaptureType.automatic:
        return MdiIcons.cameraTimer;
      case CaptureType.motionTriggered:
        return MdiIcons.motionSensor;
      case CaptureType.scheduled:
        return MdiIcons.cameraTimer;
    }
  }

  Color _getProductivityColor(double score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.orange;
    return Colors.red;
  }
}
