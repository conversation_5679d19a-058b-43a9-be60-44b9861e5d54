import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'image_model.dart';

part 'user_settings.g.dart';

@HiveType(typeId: 5)
@JsonSerializable()
class UserSettings {
  @HiveField(0)
  final bool isTrackingEnabled;
  
  @HiveField(1)
  final int captureInterval; // in seconds
  
  @HiveField(2)
  final bool motionDetectionEnabled;
  
  @HiveField(3)
  final bool locationTrackingEnabled;
  
  @HiveField(4)
  final List<PrivacyZone> privacyZones;
  
  @HiveField(5)
  final bool darkModeEnabled;
  
  @HiveField(6)
  final bool notificationsEnabled;
  
  @HiveField(7)
  final int imageQuality; // 0-100
  
  @HiveField(8)
  final int maxStorageImages;
  
  @HiveField(9)
  final bool autoDeleteOldImages;
  
  @HiveField(10)
  final int dataRetentionDays;
  
  @HiveField(11)
  final String preferredAiModel;
  
  @HiveField(12)
  final bool voiceInputEnabled;
  
  @HiveField(13)
  final bool analyticsEnabled;
  
  @HiveField(14)
  final List<String> activeHours; // e.g., ["09:00", "17:00"]
  
  @HiveField(15)
  final bool batteryOptimizationEnabled;

  const UserSettings({
    this.isTrackingEnabled = false,
    this.captureInterval = 300, // 5 minutes
    this.motionDetectionEnabled = true,
    this.locationTrackingEnabled = false,
    this.privacyZones = const [],
    this.darkModeEnabled = false,
    this.notificationsEnabled = true,
    this.imageQuality = 85,
    this.maxStorageImages = 1000,
    this.autoDeleteOldImages = true,
    this.dataRetentionDays = 90,
    this.preferredAiModel = 'gemini',
    this.voiceInputEnabled = true,
    this.analyticsEnabled = true,
    this.activeHours = const ["06:00", "22:00"],
    this.batteryOptimizationEnabled = true,
  });

  factory UserSettings.fromJson(Map<String, dynamic> json) => _$UserSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$UserSettingsToJson(this);

  UserSettings copyWith({
    bool? isTrackingEnabled,
    int? captureInterval,
    bool? motionDetectionEnabled,
    bool? locationTrackingEnabled,
    List<PrivacyZone>? privacyZones,
    bool? darkModeEnabled,
    bool? notificationsEnabled,
    int? imageQuality,
    int? maxStorageImages,
    bool? autoDeleteOldImages,
    int? dataRetentionDays,
    String? preferredAiModel,
    bool? voiceInputEnabled,
    bool? analyticsEnabled,
    List<String>? activeHours,
    bool? batteryOptimizationEnabled,
  }) {
    return UserSettings(
      isTrackingEnabled: isTrackingEnabled ?? this.isTrackingEnabled,
      captureInterval: captureInterval ?? this.captureInterval,
      motionDetectionEnabled: motionDetectionEnabled ?? this.motionDetectionEnabled,
      locationTrackingEnabled: locationTrackingEnabled ?? this.locationTrackingEnabled,
      privacyZones: privacyZones ?? this.privacyZones,
      darkModeEnabled: darkModeEnabled ?? this.darkModeEnabled,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      imageQuality: imageQuality ?? this.imageQuality,
      maxStorageImages: maxStorageImages ?? this.maxStorageImages,
      autoDeleteOldImages: autoDeleteOldImages ?? this.autoDeleteOldImages,
      dataRetentionDays: dataRetentionDays ?? this.dataRetentionDays,
      preferredAiModel: preferredAiModel ?? this.preferredAiModel,
      voiceInputEnabled: voiceInputEnabled ?? this.voiceInputEnabled,
      analyticsEnabled: analyticsEnabled ?? this.analyticsEnabled,
      activeHours: activeHours ?? this.activeHours,
      batteryOptimizationEnabled: batteryOptimizationEnabled ?? this.batteryOptimizationEnabled,
    );
  }
}

@HiveType(typeId: 6)
@JsonSerializable()
class AppState {
  @HiveField(0)
  final bool isFirstLaunch;
  
  @HiveField(1)
  final bool hasCompletedOnboarding;
  
  @HiveField(2)
  final DateTime? lastSyncTime;
  
  @HiveField(3)
  final bool isBackgroundServiceRunning;
  
  @HiveField(4)
  final int totalImagesCount;
  
  @HiveField(5)
  final int unanalyzedImagesCount;
  
  @HiveField(6)
  final String? lastError;
  
  @HiveField(7)
  final DateTime? lastErrorTime;

  const AppState({
    this.isFirstLaunch = true,
    this.hasCompletedOnboarding = false,
    this.lastSyncTime,
    this.isBackgroundServiceRunning = false,
    this.totalImagesCount = 0,
    this.unanalyzedImagesCount = 0,
    this.lastError,
    this.lastErrorTime,
  });

  factory AppState.fromJson(Map<String, dynamic> json) => _$AppStateFromJson(json);
  Map<String, dynamic> toJson() => _$AppStateToJson(this);

  AppState copyWith({
    bool? isFirstLaunch,
    bool? hasCompletedOnboarding,
    DateTime? lastSyncTime,
    bool? isBackgroundServiceRunning,
    int? totalImagesCount,
    int? unanalyzedImagesCount,
    String? lastError,
    DateTime? lastErrorTime,
  }) {
    return AppState(
      isFirstLaunch: isFirstLaunch ?? this.isFirstLaunch,
      hasCompletedOnboarding: hasCompletedOnboarding ?? this.hasCompletedOnboarding,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      isBackgroundServiceRunning: isBackgroundServiceRunning ?? this.isBackgroundServiceRunning,
      totalImagesCount: totalImagesCount ?? this.totalImagesCount,
      unanalyzedImagesCount: unanalyzedImagesCount ?? this.unanalyzedImagesCount,
      lastError: lastError ?? this.lastError,
      lastErrorTime: lastErrorTime ?? this.lastErrorTime,
    );
  }
}
