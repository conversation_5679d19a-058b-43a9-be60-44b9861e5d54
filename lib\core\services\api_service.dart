import 'dart:io';
import 'dart:convert';
import 'package:dio/dio.dart';
import '../constants/app_constants.dart';
import '../models/image_model.dart';

class ApiService {
  static ApiService? _instance;
  static ApiService get instance => _instance ??= ApiService._();
  ApiService._();

  late Dio _dio;
  
  void initialize() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.baseUrl + AppConstants.apiVersion,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors for logging and error handling
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print(obj),
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onError: (error, handler) {
        print('API Error: ${error.message}');
        handler.next(error);
      },
    ));
  }

  // Upload image to backend
  Future<Map<String, dynamic>?> uploadImage(File imageFile, ImageModel imageModel) async {
    try {
      final formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(
          imageFile.path,
          filename: '${imageModel.id}.jpg',
        ),
        'metadata': jsonEncode({
          'id': imageModel.id,
          'captured_at': imageModel.capturedAt.toIso8601String(),
          'latitude': imageModel.latitude,
          'longitude': imageModel.longitude,
          'capture_type': imageModel.captureType.name,
        }),
      });

      final response = await _dio.post(
        ApiEndpoints.uploadImage,
        data: formData,
      );

      if (response.statusCode == 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Upload error: $e');
      return null;
    }
  }

  // Get AI analysis for an image
  Future<Map<String, dynamic>?> analyzeImage(String imageId) async {
    try {
      final response = await _dio.post(
        ApiEndpoints.analyzeImage,
        data: {'image_id': imageId},
      );

      if (response.statusCode == 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Analysis error: $e');
      return null;
    }
  }

  // Get insights and analytics
  Future<Map<String, dynamic>?> getInsights({
    DateTime? startDate,
    DateTime? endDate,
    String? timeframe = 'week',
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'timeframe': timeframe,
      };

      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String();
      }

      final response = await _dio.get(
        ApiEndpoints.getInsights,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Insights error: $e');
      return null;
    }
  }

  // Send chat message and get AI response
  Future<Map<String, dynamic>?> sendChatMessage(
    String message, {
    List<String>? imageIds,
    String? context,
  }) async {
    try {
      final response = await _dio.post(
        ApiEndpoints.chat,
        data: {
          'message': message,
          'image_ids': imageIds,
          'context': context,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (response.statusCode == 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Chat error: $e');
      return null;
    }
  }

  // Get timeline data
  Future<List<Map<String, dynamic>>?> getTimeline({
    DateTime? startDate,
    DateTime? endDate,
    int? limit = 50,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'limit': limit,
      };

      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String();
      }

      final response = await _dio.get(
        ApiEndpoints.getTimeline,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['timeline']);
      }
      return null;
    } catch (e) {
      print('Timeline error: $e');
      return null;
    }
  }

  // Get analytics data
  Future<Map<String, dynamic>?> getAnalytics({
    String timeframe = 'week',
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'timeframe': timeframe,
      };

      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String();
      }

      final response = await _dio.get(
        ApiEndpoints.getAnalytics,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Analytics error: $e');
      return null;
    }
  }

  // Update user settings on backend
  Future<bool> updateSettings(Map<String, dynamic> settings) async {
    try {
      final response = await _dio.post(
        ApiEndpoints.updateSettings,
        data: settings,
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Settings update error: $e');
      return false;
    }
  }

  // Check backend health
  Future<bool> checkHealth() async {
    try {
      final response = await _dio.get('/health');
      return response.statusCode == 200;
    } catch (e) {
      print('Health check error: $e');
      return false;
    }
  }

  // Batch upload multiple images
  Future<List<String>> batchUploadImages(List<File> imageFiles, List<ImageModel> imageModels) async {
    final uploadedIds = <String>[];
    
    for (int i = 0; i < imageFiles.length; i++) {
      final result = await uploadImage(imageFiles[i], imageModels[i]);
      if (result != null) {
        uploadedIds.add(imageModels[i].id);
      }
    }
    
    return uploadedIds;
  }

  // Cancel all pending requests
  void cancelRequests() {
    _dio.close();
  }
}
