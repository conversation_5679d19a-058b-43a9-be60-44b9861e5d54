import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../models/image_model.dart';
import '../services/storage_service.dart';
import '../services/api_service.dart';
import 'app_providers.dart';

// Chat provider
final chatProvider = StateNotifierProvider<ChatNotifier, ChatState>((ref) {
  return ChatNotifier(
    ref.read(storageServiceProvider),
    ref.read(apiServiceProvider),
  );
});

class ChatState {
  final List<ChatMessage> messages;
  final bool isLoading;
  final String? error;

  const ChatState({
    this.messages = const [],
    this.isLoading = false,
    this.error,
  });

  ChatState copyWith({
    List<ChatMessage>? messages,
    bool? isLoading,
    String? error,
  }) {
    return ChatState(
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

class ChatNotifier extends StateNotifier<ChatState> {
  final StorageService _storageService;
  final ApiService _apiService;
  final Uuid _uuid = const Uuid();

  ChatNotifier(this._storageService, this._apiService) : super(const ChatState()) {
    _loadChatHistory();
  }

  Future<void> _loadChatHistory() async {
    final messages = await _storageService.getChatHistory();
    state = state.copyWith(messages: messages);
  }

  Future<void> sendMessage(
    String message, {
    List<String>? relatedImageIds,
    String? context,
  }) async {
    if (message.trim().isEmpty) return;

    // Add user message
    final userMessage = ChatMessage(
      id: _uuid.v4(),
      message: message,
      isUser: true,
      timestamp: DateTime.now(),
      relatedImageIds: relatedImageIds,
      context: context,
    );

    await _storageService.saveChatMessage(userMessage);
    state = state.copyWith(
      messages: [...state.messages, userMessage],
      isLoading: true,
      error: null,
    );

    try {
      // Send to API and get response
      final response = await _apiService.sendChatMessage(
        message,
        imageIds: relatedImageIds,
        context: context,
      );

      if (response != null) {
        final aiMessage = ChatMessage(
          id: _uuid.v4(),
          message: response['response'] ?? 'Sorry, I couldn\'t process your request.',
          isUser: false,
          timestamp: DateTime.now(),
          relatedImageIds: response['related_images']?.cast<String>(),
          context: response['context'],
        );

        await _storageService.saveChatMessage(aiMessage);
        state = state.copyWith(
          messages: [...state.messages, aiMessage],
          isLoading: false,
        );
      } else {
        throw Exception('Failed to get response from AI');
      }
    } catch (e) {
      // Add error message
      final errorMessage = ChatMessage(
        id: _uuid.v4(),
        message: 'Sorry, I encountered an error: ${e.toString()}',
        isUser: false,
        timestamp: DateTime.now(),
      );

      await _storageService.saveChatMessage(errorMessage);
      state = state.copyWith(
        messages: [...state.messages, errorMessage],
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> sendQuickQuery(String query) async {
    final quickQueries = {
      'morning': 'What did I do this morning?',
      'productive': 'Show me my most productive hours today',
      'activities': 'What activities did I do today?',
      'summary': 'Give me a summary of my day',
      'focus': 'When was I most focused today?',
      'breaks': 'How many breaks did I take?',
    };

    final message = quickQueries[query] ?? query;
    await sendMessage(message);
  }

  Future<void> clearChat() async {
    await _storageService.clearChatHistory();
    state = state.copyWith(messages: []);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  Future<void> refreshChat() async {
    await _loadChatHistory();
  }
}

// Quick queries provider
final quickQueriesProvider = Provider<List<QuickQuery>>((ref) {
  return [
    QuickQuery(
      id: 'morning',
      title: 'Morning Activities',
      query: 'What did I do this morning?',
      icon: '🌅',
    ),
    QuickQuery(
      id: 'productive',
      title: 'Productive Hours',
      query: 'Show me my most productive hours today',
      icon: '⚡',
    ),
    QuickQuery(
      id: 'activities',
      title: 'Today\'s Activities',
      query: 'What activities did I do today?',
      icon: '📋',
    ),
    QuickQuery(
      id: 'summary',
      title: 'Day Summary',
      query: 'Give me a summary of my day',
      icon: '📊',
    ),
    QuickQuery(
      id: 'focus',
      title: 'Focus Time',
      query: 'When was I most focused today?',
      icon: '🎯',
    ),
    QuickQuery(
      id: 'breaks',
      title: 'Break Analysis',
      query: 'How many breaks did I take?',
      icon: '☕',
    ),
  ];
});

class QuickQuery {
  final String id;
  final String title;
  final String query;
  final String icon;

  const QuickQuery({
    required this.id,
    required this.title,
    required this.query,
    required this.icon,
  });
}

// Voice input provider
final voiceInputProvider = StateNotifierProvider<VoiceInputNotifier, VoiceInputState>((ref) {
  return VoiceInputNotifier();
});

class VoiceInputState {
  final bool isListening;
  final bool isAvailable;
  final String recognizedText;
  final String? error;

  const VoiceInputState({
    this.isListening = false,
    this.isAvailable = false,
    this.recognizedText = '',
    this.error,
  });

  VoiceInputState copyWith({
    bool? isListening,
    bool? isAvailable,
    String? recognizedText,
    String? error,
  }) {
    return VoiceInputState(
      isListening: isListening ?? this.isListening,
      isAvailable: isAvailable ?? this.isAvailable,
      recognizedText: recognizedText ?? this.recognizedText,
      error: error,
    );
  }
}

class VoiceInputNotifier extends StateNotifier<VoiceInputState> {
  VoiceInputNotifier() : super(const VoiceInputState()) {
    _initializeSpeechToText();
  }

  Future<void> _initializeSpeechToText() async {
    // TODO: Implement speech to text initialization
    // This will be implemented when we add the speech_to_text functionality
    state = state.copyWith(isAvailable: true);
  }

  Future<void> startListening() async {
    if (!state.isAvailable) return;

    try {
      // TODO: Implement speech to text listening
      state = state.copyWith(
        isListening: true,
        error: null,
        recognizedText: '',
      );
    } catch (e) {
      state = state.copyWith(
        isListening: false,
        error: e.toString(),
      );
    }
  }

  Future<void> stopListening() async {
    // TODO: Implement stop listening
    state = state.copyWith(isListening: false);
  }

  void updateRecognizedText(String text) {
    state = state.copyWith(recognizedText: text);
  }

  void clearText() {
    state = state.copyWith(recognizedText: '');
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}
