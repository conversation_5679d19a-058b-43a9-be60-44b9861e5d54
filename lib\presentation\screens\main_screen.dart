import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../../core/constants/app_constants.dart';
import 'dashboard_screen.dart';
import 'timeline_screen.dart';
import 'analytics_screen.dart';
import 'chat_screen.dart';
import 'gallery_screen.dart';
import 'settings_screen.dart';
import 'camera_screen.dart';

final currentIndexProvider = StateProvider<int>((ref) => 0);

class MainScreen extends ConsumerWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(currentIndexProvider);

    final screens = [
      const DashboardScreen(),
      const TimelineScreen(),
      const AnalyticsScreen(),
      const ChatScreen(),
      const GalleryScreen(),
      const SettingsScreen(),
    ];

    return Scaffold(
      body: IndexedStack(
        index: currentIndex,
        children: screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: (index) => ref.read(currentIndexProvider.notifier).state = index,
        type: BottomNavigationBarType.fixed,
        items: [
          BottomNavigationBarItem(
            icon: Icon(MdiIcons.viewDashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(MdiIcons.timeline),
            label: 'Timeline',
          ),
          BottomNavigationBarItem(
            icon: Icon(MdiIcons.chartLine),
            label: 'Analytics',
          ),
          BottomNavigationBarItem(
            icon: Icon(MdiIcons.chat),
            label: 'Chat',
          ),
          BottomNavigationBarItem(
            icon: Icon(MdiIcons.imageMultiple),
            label: 'Gallery',
          ),
          BottomNavigationBarItem(
            icon: Icon(MdiIcons.cog),
            label: 'Settings',
          ),
        ],
      ),
      floatingActionButton: currentIndex == 0 || currentIndex == 4
          ? FloatingActionButton(
              onPressed: () => _showCaptureDialog(context),
              child: Icon(MdiIcons.camera),
            )
          : null,
    );
  }

  void _showCaptureDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Capture Options',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: AppSizes.paddingL),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _CaptureOption(
                  icon: MdiIcons.camera,
                  label: 'Take Photo',
                  onTap: () {
                    Navigator.pop(context);
                    _openCamera(context);
                  },
                ),
                _CaptureOption(
                  icon: MdiIcons.cameraTimer,
                  label: 'Start Timer',
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Implement timer capture
                  },
                ),
                _CaptureOption(
                  icon: MdiIcons.motionSensor,
                  label: 'Motion Mode',
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Implement motion detection
                  },
                ),
              ],
            ),
            const SizedBox(height: AppSizes.paddingL),
          ],
        ),
      ),
    );
  }

  void _openCamera(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CameraScreen(),
      ),
    );
  }
}

class _CaptureOption extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;

  const _CaptureOption({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppSizes.radiusL),
      child: Container(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.outline,
          ),
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: AppSizes.iconXL,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: AppSizes.paddingS),
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
