import 'dart:io';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import '../models/image_model.dart';
import '../models/user_settings.dart';
import '../constants/app_constants.dart';

class StorageService {
  static StorageService? _instance;
  static StorageService get instance => _instance ??= StorageService._();
  StorageService._();

  late Box<ImageModel> _imagesBox;
  late Box<AnalyticsData> _analyticsBox;
  late Box<ChatMessage> _chatBox;
  late Box<UserSettings> _settingsBox;
  late Box<AppState> _appStateBox;

  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    await Hive.initFlutter();
    
    // Register adapters
    Hive.registerAdapter(ImageModelAdapter());
    Hive.registerAdapter(CaptureTypeAdapter());
    Hive.registerAdapter(AnalyticsDataAdapter());
    Hive.registerAdapter(ChatMessageAdapter());
    Hive.registerAdapter(PrivacyZoneAdapter());
    Hive.registerAdapter(UserSettingsAdapter());
    Hive.registerAdapter(AppStateAdapter());

    // Open boxes
    _imagesBox = await Hive.openBox<ImageModel>(AppConstants.imagesBox);
    _analyticsBox = await Hive.openBox<AnalyticsData>(AppConstants.analyticsBox);
    _chatBox = await Hive.openBox<ChatMessage>(AppConstants.chatBox);
    _settingsBox = await Hive.openBox<UserSettings>(AppConstants.userPrefsBox);
    _appStateBox = await Hive.openBox<AppState>('app_state');

    _isInitialized = true;
  }

  // Image operations
  Future<void> saveImage(ImageModel image) async {
    await _imagesBox.put(image.id, image);
  }

  Future<ImageModel?> getImage(String id) async {
    return _imagesBox.get(id);
  }

  Future<List<ImageModel>> getAllImages() async {
    return _imagesBox.values.toList();
  }

  Future<List<ImageModel>> getImagesByDateRange(DateTime start, DateTime end) async {
    return _imagesBox.values
        .where((image) => 
            image.capturedAt.isAfter(start) && 
            image.capturedAt.isBefore(end))
        .toList();
  }

  Future<void> deleteImage(String id) async {
    await _imagesBox.delete(id);
  }

  Future<void> deleteOldImages(int retentionDays) async {
    final cutoffDate = DateTime.now().subtract(Duration(days: retentionDays));
    final oldImages = _imagesBox.values
        .where((image) => image.capturedAt.isBefore(cutoffDate))
        .toList();
    
    for (final image in oldImages) {
      await _imagesBox.delete(image.id);
    }
  }

  // Analytics operations
  Future<void> saveAnalytics(AnalyticsData analytics) async {
    final key = analytics.date.toIso8601String().split('T')[0]; // Use date as key
    await _analyticsBox.put(key, analytics);
  }

  Future<AnalyticsData?> getAnalytics(DateTime date) async {
    final key = date.toIso8601String().split('T')[0];
    return _analyticsBox.get(key);
  }

  Future<List<AnalyticsData>> getAnalyticsRange(DateTime start, DateTime end) async {
    return _analyticsBox.values
        .where((analytics) => 
            analytics.date.isAfter(start.subtract(const Duration(days: 1))) && 
            analytics.date.isBefore(end.add(const Duration(days: 1))))
        .toList();
  }

  // Chat operations
  Future<void> saveChatMessage(ChatMessage message) async {
    await _chatBox.put(message.id, message);
    
    // Keep only the latest messages
    if (_chatBox.length > AppConstants.maxChatHistory) {
      final messages = _chatBox.values.toList()
        ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
      
      // Delete oldest messages
      for (int i = AppConstants.maxChatHistory; i < messages.length; i++) {
        await _chatBox.delete(messages[i].id);
      }
    }
  }

  Future<List<ChatMessage>> getChatHistory() async {
    final messages = _chatBox.values.toList();
    messages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    return messages;
  }

  Future<void> clearChatHistory() async {
    await _chatBox.clear();
  }

  // Settings operations
  Future<void> saveSettings(UserSettings settings) async {
    await _settingsBox.put('settings', settings);
  }

  Future<UserSettings> getSettings() async {
    return _settingsBox.get('settings') ?? const UserSettings();
  }

  // App state operations
  Future<void> saveAppState(AppState state) async {
    await _appStateBox.put('state', state);
  }

  Future<AppState> getAppState() async {
    return _appStateBox.get('state') ?? const AppState();
  }

  // Utility methods
  Future<int> getTotalImagesCount() async {
    return _imagesBox.length;
  }

  Future<int> getUnanalyzedImagesCount() async {
    return _imagesBox.values.where((image) => !image.isAnalyzed).length;
  }

  Future<double> getStorageSize() async {
    final dir = await getApplicationDocumentsDirectory();
    int totalSize = 0;
    
    await for (final entity in dir.list(recursive: true)) {
      if (entity is File) {
        totalSize += await entity.length();
      }
    }
    
    return totalSize / (1024 * 1024); // Return size in MB
  }

  Future<void> clearAllData() async {
    await _imagesBox.clear();
    await _analyticsBox.clear();
    await _chatBox.clear();
    await _settingsBox.clear();
    await _appStateBox.clear();
  }

  Future<void> dispose() async {
    await _imagesBox.close();
    await _analyticsBox.close();
    await _chatBox.close();
    await _settingsBox.close();
    await _appStateBox.close();
  }
}
